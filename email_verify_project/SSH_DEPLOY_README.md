# 树状SSH部署工具使用说明

## 概述

`tree_ssh_deploy.py` 是一个强大的树状SSH分发部署脚本，支持：
- 🌳 树状结构异步并发连接（每层最多5个节点）
- 📦 自动文件打包上传
- 🔧 自动环境安装配置
- 🚀 服务启动/停止/重启管理
- 📊 Redis状态统一管理
- 🛠️ 自定义命令执行

## 功能特性

### ✅ 已解决的问题
- **SSH后台启动卡死问题**：使用screen会话避免SSH连接卡死
- **Ubuntu 24 pip问题**：自动使用`--break-system-packages`参数
- **阿里云镜像配置**：自动配置pip阿里云镜像源
- **文件路径问题**：自动展开`~`路径到用户家目录
- **Redis数据类型**：统一转换为字符串类型

### 🔧 核心功能
1. **deploy** - 完整项目部署
2. **start** - 启动服务（使用screen会话）
3. **stop** - 停止服务
4. **restart** - 重启服务
5. **status** - 检查服务状态
6. **command** - 执行自定义命令
7. **start-simple** - 简单启动（使用systemd-run）

## 使用方法

### 基本命令

```bash
# 1. 部署项目到所有服务器
python3 tree_ssh_deploy.py deploy

# 2. 启动服务
python3 tree_ssh_deploy.py start

# 3. 停止服务
python3 tree_ssh_deploy.py stop

# 4. 重启服务
python3 tree_ssh_deploy.py restart

# 5. 检查服务状态
python3 tree_ssh_deploy.py status

# 6. 执行自定义命令
python3 tree_ssh_deploy.py command -c "ls -la ~/email_verify_deploy/"

# 7. 指定配置文件
python3 tree_ssh_deploy.py deploy -f custom_ssh.json
```

### 高级用法

```bash
# 检查远程Python环境
python3 tree_ssh_deploy.py command -c "python3 --version && pip3 --version"

# 查看远程日志
python3 tree_ssh_deploy.py command -c "tail -f ~/email_verify_deploy/logs/email_verify.log"

# 检查screen会话
python3 tree_ssh_deploy.py command -c "screen -ls"

# 手动进入screen会话（需要SSH登录）
# screen -r email_verify

# 检查系统资源
python3 tree_ssh_deploy.py command -c "free -h && df -h"
```

## 配置文件

### SSH配置文件格式 (ssh.json)

```json
[
  {
    "host": "************",
    "port": 22,
    "username": "ubuntu",
    "password": "your_password"
  },
  {
    "host": "************", 
    "port": 22,
    "username": "ubuntu",
    "password": "your_password"
  }
]
```

### 自动上传的文件列表

脚本会自动上传以下文件到远程服务器的`~/email_verify_deploy/`目录：

- `.env` - 环境变量配置
- `config.py` - 项目配置文件
- `email_verify.py` - 主程序
- `install.sh` - 安装脚本
- `node_utils.py` - 节点工具
- `pow_utils.py` - PoW工具
- `pow_worker.py` - PoW工作进程
- `powlib.so` - PoW库文件
- `request_builder.py` - 请求构建器
- `requirements.txt` - Python依赖
- `test10.py` - 测试脚本
- `x64hash.py` - 哈希工具

## 树状结构说明

### 分层逻辑
- **第0层（根层）**：最多5个节点，直接从本地连接
- **第1层**：每个第0层节点最多连接5个子节点
- **第N层**：每个第N-1层节点最多连接5个子节点

### 示例结构
```
本地
├── 节点1 (第0层)
│   ├── 节点6 (第1层)
│   ├── 节点7 (第1层)
│   └── ...
├── 节点2 (第0层)
│   ├── 节点11 (第1层)
│   └── ...
└── ...
```

## 安装脚本特性

### install.sh 自动处理
- ✅ Ubuntu版本检测
- ✅ 网络连接检查
- ✅ Python环境安装
- ✅ pip配置和升级
- ✅ 阿里云镜像配置
- ✅ 依赖包安装（支持--break-system-packages）
- ✅ 启动脚本生成

### 生成的脚本
- `start.sh` - 服务启动脚本
- `stop.sh` - 服务停止脚本

## Redis状态管理

### 状态键名
- `ssh:nodes:{host}` - SSH节点状态
- `ssh:deploy:{host}` - 部署状态
- `ssh:tasks` - 任务队列

### 状态信息
- 连接状态（pending/connecting/connected/failed）
- 部署时间和结果
- 错误信息记录

## 故障排除

### 常见问题

**Q: SSH连接失败怎么办？**
A: 检查网络连接、SSH配置、防火墙设置

**Q: 服务启动后立即停止？**
A: 检查Python环境、依赖包安装、配置文件

**Q: pip安装失败？**
A: 脚本已自动处理Ubuntu 24的pip问题，使用--break-system-packages参数

**Q: 文件上传失败？**
A: 检查磁盘空间、权限设置、网络稳定性

### 调试命令

```bash
# 检查远程环境
python3 tree_ssh_deploy.py command -c "whoami && pwd && python3 --version"

# 检查安装结果
python3 tree_ssh_deploy.py command -c "ls -la ~/email_verify_deploy/"

# 检查Python包
python3 tree_ssh_deploy.py command -c "cd ~/email_verify_deploy && python3 -c 'import redis, aiohttp; print(\"OK\")'"

# 手动测试启动
python3 tree_ssh_deploy.py command -c "cd ~/email_verify_deploy && ./start.sh"
```

## 最佳实践

1. **部署前测试**：先在单台服务器测试部署流程
2. **分批部署**：大量服务器建议分批部署
3. **监控日志**：部署后检查服务运行日志
4. **备份配置**：重要配置文件做好备份
5. **网络稳定**：确保网络连接稳定

## 技术实现

- **异步并发**：使用asyncio和asyncssh实现高效并发
- **错误处理**：完善的异常捕获和重试机制
- **状态管理**：Redis统一管理所有节点状态
- **日志记录**：详细的操作日志和状态跟踪
- **兼容性**：支持多种Ubuntu版本和SSH配置
