#!/bin/bash

# 健壮的Ubuntu安装脚本
# 适用于新安装的Ubuntu系统，自动安装Python依赖

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到以root用户运行，建议使用普通用户运行此脚本"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "安装已取消"
            exit 1
        fi
    fi
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    if ! ping -c 1 8.8.8.8 &> /dev/null; then
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    log_success "网络连接正常"
}

# 更新系统包列表（防止卡死）
update_system() {
    log_info "更新系统包列表..."
    
    # 设置非交互模式，防止卡死
    export DEBIAN_FRONTEND=noninteractive
    
    # 配置dpkg以避免交互式提示
    echo 'DPkg::Options {
        "--force-confdef";
        "--force-confold";
    }' | sudo tee /etc/apt/apt.conf.d/50unattended-upgrades-local > /dev/null
    
    # 更新包列表，设置超时
    timeout 300 sudo apt-get update -y || {
        log_warning "apt-get update超时或失败，尝试修复..."
        sudo apt-get clean
        sudo rm -rf /var/lib/apt/lists/*
        sudo apt-get update -y
    }
    
    log_success "系统包列表更新完成"
}

# 安装基础工具
install_basic_tools() {
    log_info "安装基础工具..."
    
    local packages=(
        "curl"
        "wget" 
        "software-properties-common"
        "apt-transport-https"
        "ca-certificates"
        "gnupg"
        "lsb-release"
        "build-essential"
        "git"
    )
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            log_info "安装 $package..."
            sudo apt-get install -y "$package"
        else
            log_info "$package 已安装"
        fi
    done
    
    log_success "基础工具安装完成"
}

# 检查Python版本
check_python() {
    log_info "检查Python安装..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
        log_success "Python3已安装，版本: $PYTHON_VERSION"
        
        # 检查Python版本是否满足要求（至少3.7）
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
        
        if [[ $PYTHON_MAJOR -lt 3 ]] || [[ $PYTHON_MAJOR -eq 3 && $PYTHON_MINOR -lt 7 ]]; then
            log_warning "Python版本过低，建议升级到3.7或更高版本"
        fi
    else
        log_info "Python3未安装，正在安装..."
        sudo apt-get install -y python3 python3-dev
        log_success "Python3安装完成"
    fi
}

# 检查并安装pip
check_and_install_pip() {
    log_info "检查pip安装..."
    
    if command -v pip3 &> /dev/null; then
        PIP_VERSION=$(pip3 --version 2>&1 | cut -d' ' -f2)
        log_success "pip3已安装，版本: $PIP_VERSION"
    else
        log_info "pip3未安装，正在安装..."
        
        # 尝试通过apt安装
        if sudo apt-get install -y python3-pip; then
            log_success "通过apt安装pip3成功"
        else
            log_warning "通过apt安装pip3失败，尝试使用get-pip.py..."
            
            # 下载并安装get-pip.py
            curl -sS https://bootstrap.pypa.io/get-pip.py -o get-pip.py
            python3 get-pip.py --user
            rm -f get-pip.py
            
            # 添加到PATH
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
            export PATH="$HOME/.local/bin:$PATH"
            
            log_success "通过get-pip.py安装pip3成功"
        fi
    fi
    
    # 升级pip到最新版本
    log_info "升级pip到最新版本..."
    python3 -m pip install --upgrade pip --user
    log_success "pip升级完成"
}

# 安装Python依赖
install_requirements() {
    log_info "安装Python依赖..."
    
    # 检查requirements.txt是否存在
    if [[ ! -f "requirements.txt" ]]; then
        log_error "requirements.txt文件不存在"
        exit 1
    fi
    
    log_info "找到requirements.txt文件，内容如下："
    cat requirements.txt
    echo
    
    # 创建虚拟环境（可选）
    read -p "是否创建Python虚拟环境？(推荐) (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        USE_VENV=false
    else
        USE_VENV=true
    fi
    
    if [[ $USE_VENV == true ]]; then
        log_info "安装python3-venv..."
        sudo apt-get install -y python3-venv
        
        log_info "创建虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
        log_success "虚拟环境已激活"
        
        # 升级虚拟环境中的pip
        pip install --upgrade pip
    fi
    
    # 安装依赖，添加重试机制
    local max_retries=3
    local retry_count=0
    
    while [[ $retry_count -lt $max_retries ]]; do
        log_info "安装requirements.txt中的依赖 (尝试 $((retry_count + 1))/$max_retries)..."
        
        if pip3 install -r requirements.txt; then
            log_success "Python依赖安装完成"
            break
        else
            retry_count=$((retry_count + 1))
            if [[ $retry_count -lt $max_retries ]]; then
                log_warning "安装失败，30秒后重试..."
                sleep 30
            else
                log_error "Python依赖安装失败，已达到最大重试次数"
                exit 1
            fi
        fi
    done
    
    # 显示安装的包
    log_info "已安装的Python包："
    pip3 list
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."
    
    cat > start.sh << 'EOF'
#!/bin/bash

# 项目启动脚本

# 激活虚拟环境（如果存在）
if [[ -d "venv" ]]; then
    source venv/bin/activate
    echo "虚拟环境已激活"
fi

# 检查主程序文件
if [[ -f "email_verify.py" ]]; then
    echo "启动邮箱验证程序..."
    python3 email_verify.py
elif [[ -f "main.py" ]]; then
    echo "启动主程序..."
    python3 main.py
else
    echo "未找到主程序文件，请手动指定要运行的Python文件"
    echo "可用的Python文件："
    ls *.py 2>/dev/null || echo "当前目录没有Python文件"
fi
EOF
    
    chmod +x start.sh
    log_success "启动脚本创建完成: start.sh"
}

# 系统信息检查
system_info() {
    log_info "系统信息："
    echo "操作系统: $(lsb_release -d | cut -f2)"
    echo "内核版本: $(uname -r)"
    echo "Python版本: $(python3 --version 2>&1)"
    echo "pip版本: $(pip3 --version 2>&1)"
    echo "可用内存: $(free -h | grep '^Mem:' | awk '{print $7}')"
    echo "磁盘空间: $(df -h . | tail -1 | awk '{print $4}')"
}

# 主函数
main() {
    log_info "开始Ubuntu环境安装配置..."
    echo "========================================"
    
    # 系统信息
    system_info
    echo "========================================"
    
    # 执行安装步骤
    check_root
    check_network
    update_system
    install_basic_tools
    check_python
    check_and_install_pip
    install_requirements
    create_startup_script
    
    echo "========================================"
    log_success "安装完成！"
    echo
    log_info "使用说明："
    echo "1. 运行 './start.sh' 启动程序"
    echo "2. 如果创建了虚拟环境，请使用 'source venv/bin/activate' 激活"
    echo "3. 查看日志文件了解程序运行状态"
    echo
    log_info "如果遇到问题，请检查："
    echo "- 网络连接是否正常"
    echo "- Python版本是否兼容"
    echo "- 依赖包是否正确安装"
}

# 捕获中断信号
trap 'log_error "安装被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
