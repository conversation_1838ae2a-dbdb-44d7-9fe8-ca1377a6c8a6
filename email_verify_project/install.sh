#!/bin/bash

# 健壮的Ubuntu安装脚本 - 一键安装所有依赖
# 适用于新安装的Ubuntu系统，自动安装Python依赖
# 防止卡死，自动重试，完整错误处理

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示系统信息
show_system_info() {
    log_info "系统信息："
    echo "操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || echo "Unknown")"
    echo "内核版本: $(uname -r)"
    echo "架构: $(uname -m)"
    echo "可用内存: $(free -h | grep '^Mem:' | awk '{print $7}' || echo "Unknown")"
    echo "磁盘空间: $(df -h . | tail -1 | awk '{print $4}' || echo "Unknown")"
    echo "========================================"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    local test_urls=("8.8.8.8" "114.114.114.114" "1.1.1.1")

    for url in "${test_urls[@]}"; do
        if ping -c 1 -W 5 "$url" &> /dev/null; then
            log_success "网络连接正常 (测试地址: $url)"
            return 0
        fi
    done

    log_error "网络连接失败，请检查网络设置"
    exit 1
}

# 更新系统包列表（防止卡死）
update_system() {
    log_info "更新系统包列表..."

    # 设置非交互模式，防止卡死
    export DEBIAN_FRONTEND=noninteractive

    # 配置dpkg以避免交互式提示
    sudo tee /etc/apt/apt.conf.d/99local-install > /dev/null << 'EOF'
DPkg::Options {
    "--force-confdef";
    "--force-confold";
}
APT::Get::Assume-Yes "true";
APT::Get::Fix-Broken "true";
EOF

    # 更新包列表，设置超时和重试
    local max_retries=3
    local retry_count=0

    while [[ $retry_count -lt $max_retries ]]; do
        log_info "更新包列表 (尝试 $((retry_count + 1))/$max_retries)..."

        if timeout 300 sudo apt-get update -y; then
            log_success "系统包列表更新完成"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [[ $retry_count -lt $max_retries ]]; then
                log_warning "更新失败，清理缓存后重试..."
                sudo apt-get clean
                sudo rm -rf /var/lib/apt/lists/*
                sleep 10
            else
                log_error "系统更新失败，已达到最大重试次数"
                exit 1
            fi
        fi
    done
}

# 安装基础工具
install_basic_tools() {
    log_info "安装基础工具..."

    local essential_packages=(
        "curl"
        "wget"
        "software-properties-common"
        "apt-transport-https"
        "ca-certificates"
        "gnupg"
        "lsb-release"
        "build-essential"
        "git"
        "unzip"
        "vim"
    )

    # 批量安装，提高效率
    log_info "批量安装基础工具包..."
    sudo apt-get install -y "${essential_packages[@]}" || {
        log_warning "批量安装失败，尝试逐个安装..."
        for package in "${essential_packages[@]}"; do
            if ! dpkg -l | grep -q "^ii  $package "; then
                log_info "安装 $package..."
                sudo apt-get install -y "$package" || log_warning "$package 安装失败，跳过"
            fi
        done
    }

    log_success "基础工具安装完成"
}

# 检查并安装Python
install_python() {
    log_info "检查Python安装..."

    # 安装Python相关包
    local python_packages=(
        "python3"
        "python3-pip"
        "python3-dev"
        "python3-venv"
        "python3-setuptools"
        "python3-wheel"
    )

    log_info "安装Python环境..."
    sudo apt-get install -y "${python_packages[@]}"

    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
        log_success "Python3安装完成，版本: $PYTHON_VERSION"

        # 检查Python版本
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

        if [[ $PYTHON_MAJOR -lt 3 ]] || [[ $PYTHON_MAJOR -eq 3 && $PYTHON_MINOR -lt 7 ]]; then
            log_warning "Python版本 $PYTHON_VERSION 可能过低，建议使用3.7+"
        fi
    else
        log_error "Python3安装失败"
        exit 1
    fi
}

# 检查并配置pip
setup_pip() {
    log_info "配置pip环境..."

    # 检测Ubuntu版本
    UBUNTU_VERSION=""
    if command -v lsb_release &> /dev/null; then
        UBUNTU_VERSION=$(lsb_release -rs 2>/dev/null)
        log_info "检测到Ubuntu版本: $UBUNTU_VERSION"
    fi

    # 确保pip可用
    if ! command -v pip3 &> /dev/null; then
        log_info "pip3不可用，尝试替代方案..."
        # 使用get-pip.py作为备选
        curl -sS https://bootstrap.pypa.io/get-pip.py -o get-pip.py
        python3 get-pip.py --user
        rm -f get-pip.py

        # 添加到PATH
        export PATH="$HOME/.local/bin:$PATH"
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    fi

    # 检查是否需要使用--break-system-packages（Ubuntu 24+）
    PIP_EXTRA_ARGS=""
    if [[ -n "$UBUNTU_VERSION" ]] && [[ "${UBUNTU_VERSION%%.*}" -ge 24 ]]; then
        log_warning "检测到Ubuntu 24+，使用--break-system-packages参数"
        PIP_EXTRA_ARGS="--break-system-packages"
    fi

    # 升级pip
    log_info "升级pip到最新版本..."
    if [[ -n "$PIP_EXTRA_ARGS" ]]; then
        python3 -m pip install --upgrade pip $PIP_EXTRA_ARGS || {
            log_warning "pip升级失败，尝试用户安装..."
            python3 -m pip install --upgrade pip --user || {
                log_warning "pip升级失败，继续使用当前版本"
            }
        }
    else
        python3 -m pip install --upgrade pip --user || {
            log_warning "pip升级失败，继续使用当前版本"
        }
    fi

    # 显示pip版本
    if command -v pip3 &> /dev/null; then
        PIP_VERSION=$(pip3 --version 2>&1 | cut -d' ' -f2)
        log_success "pip3配置完成，版本: $PIP_VERSION"
    else
        log_success "pip配置完成，使用 python3 -m pip"
    fi

    # 导出pip参数供后续使用
    export PIP_INSTALL_ARGS="$PIP_EXTRA_ARGS"
}

# 安装Python依赖
install_requirements() {
    log_info "安装Python依赖..."

    # 检查requirements.txt是否存在
    if [[ ! -f "requirements.txt" ]]; then
        log_error "requirements.txt文件不存在"
        log_info "当前目录内容："
        ls -la
        exit 1
    fi

    log_info "找到requirements.txt文件，内容如下："
    cat requirements.txt
    echo "========================================"

    # 安装依赖，添加重试机制
    local max_retries=3
    local retry_count=0
    local pip_cmd="pip3"

    # 检查pip命令可用性
    if ! command -v pip3 &> /dev/null; then
        pip_cmd="python3 -m pip"
        log_info "使用 python3 -m pip 安装依赖"
    fi

    while [[ $retry_count -lt $max_retries ]]; do
        log_info "安装requirements.txt中的依赖 (尝试 $((retry_count + 1))/$max_retries)..."

        # 构建安装命令
        install_cmd="$pip_cmd install -r requirements.txt --no-cache-dir"

        # 添加Ubuntu 24+的特殊参数
        if [[ -n "$PIP_INSTALL_ARGS" ]]; then
            install_cmd="$install_cmd $PIP_INSTALL_ARGS"
            log_info "使用参数: $PIP_INSTALL_ARGS"
        else
            install_cmd="$install_cmd --user"
        fi

        log_info "执行命令: $install_cmd"

        # 尝试安装依赖
        if eval $install_cmd; then
            log_success "Python依赖安装完成"
            break
        else
            retry_count=$((retry_count + 1))
            if [[ $retry_count -lt $max_retries ]]; then
                log_warning "安装失败，30秒后重试..."
                log_info "尝试清理pip缓存..."
                $pip_cmd cache purge 2>/dev/null || true
                sleep 30
            else
                log_error "Python依赖安装失败，已达到最大重试次数"
                log_info "尝试逐个安装依赖包..."
                install_packages_individually
                break
            fi
        fi
    done

    # 验证安装
    verify_installation
}

# 逐个安装依赖包（备用方案）
install_packages_individually() {
    log_info "尝试逐个安装依赖包..."
    local pip_cmd="pip3"

    if ! command -v pip3 &> /dev/null; then
        pip_cmd="python3 -m pip"
    fi

    # 读取requirements.txt并逐个安装
    while IFS= read -r package; do
        # 跳过空行和注释
        [[ -z "$package" || "$package" =~ ^#.*$ ]] && continue

        log_info "安装 $package..."

        # 构建安装命令
        install_cmd="$pip_cmd install \"$package\" --no-cache-dir"

        # 添加Ubuntu 24+的特殊参数
        if [[ -n "$PIP_INSTALL_ARGS" ]]; then
            install_cmd="$install_cmd $PIP_INSTALL_ARGS"
        else
            install_cmd="$install_cmd --user"
        fi

        if eval $install_cmd; then
            log_success "$package 安装成功"
        else
            log_warning "$package 安装失败，跳过"
        fi
    done < requirements.txt
}

# 验证安装
verify_installation() {
    log_info "验证Python包安装..."
    local pip_cmd="pip3"

    if ! command -v pip3 &> /dev/null; then
        pip_cmd="python3 -m pip"
    fi

    # 检查关键包是否安装成功
    local key_packages=("redis" "aiohttp" "requests" "psutil" "asyncssh")
    local failed_packages=()

    for package in "${key_packages[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            log_success "$package 验证通过"
        else
            log_warning "$package 验证失败"
            failed_packages+=("$package")
        fi
    done

    if [[ ${#failed_packages[@]} -gt 0 ]]; then
        log_warning "以下包验证失败: ${failed_packages[*]}"
        log_info "可能需要手动安装这些包"
    else
        log_success "所有关键包验证通过"
    fi

    # 显示已安装的包
    log_info "已安装的Python包："
    $pip_cmd list --user 2>/dev/null | head -20 || echo "无法获取包列表"
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."

    cat > start.sh << 'EOF'
#!/bin/bash

# 项目启动脚本
echo "=== 邮箱验证项目启动脚本 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: Python3未安装"
    exit 1
fi

echo "Python版本: $(python3 --version)"

# 检查主程序文件
if [[ -f "email_verify.py" ]]; then
    echo "启动邮箱验证程序..."
    python3 email_verify.py
elif [[ -f "main.py" ]]; then
    echo "启动主程序..."
    python3 main.py
else
    echo "未找到主程序文件，可用的Python文件："
    ls *.py 2>/dev/null || echo "当前目录没有Python文件"
    echo "请手动指定要运行的Python文件"
fi
EOF

    chmod +x start.sh
    log_success "启动脚本创建完成: start.sh"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    sudo rm -f /etc/apt/apt.conf.d/99local-install
    log_success "清理完成"
}

# 主函数
main() {
    echo "🚀 Ubuntu环境一键安装脚本"
    echo "========================================"

    # 显示系统信息
    show_system_info

    # 执行安装步骤
    check_network
    update_system
    install_basic_tools
    install_python
    setup_pip
    install_requirements
    create_startup_script
    cleanup

    echo "========================================"
    log_success "🎉 安装完成！"
    echo
    log_info "📋 使用说明："
    echo "1. 运行程序: ./start.sh"
    echo "2. 直接运行: python3 email_verify.py"
    echo "3. 查看日志: tail -f logs/email_verify.log"
    echo
    log_info "🔧 如果遇到问题："
    echo "- 检查网络连接"
    echo "- 确认Python版本兼容性"
    echo "- 查看错误日志"
    echo
    log_success "✅ 环境配置完成，可以开始使用！"
}

# 捕获中断信号
trap 'log_error "安装被中断，正在清理..."; cleanup; exit 1' INT TERM

# 运行主函数
main "$@"
