import json
import os
import time
import logging
import powlib
import request_builder
import redis
import socket

# 导入配置和工具模块
from config import (
    REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD,
    RedisKeys, POW_MAX_COUNT, POW_MAX_AGE, POW_MAX_SOLVE_TIME,
    REQUEST_INTERVAL, PROXY_URL, setup_logger
)
from node_utils import get_node_id, get_node_ip
# 设置日志
logger = setup_logger(__name__, "pow_worker.log")

# 获取节点信息
NODE_ID = get_node_id()
NODE_IP = get_node_ip()

# 使用NODE_IP构建节点状态键
NODE_STATUS_KEY = f"{RedisKeys.POW_NODE_PREFIX}:{NODE_IP}"

logger.info(f"节点ID: {NODE_ID}")
logger.info(f"节点IP: {NODE_IP}")
logger.info(f"节点状态键: {NODE_STATUS_KEY}")

# Redis客户端初始化
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=True,
    socket_timeout=5,
    socket_connect_timeout=5
)

# 更新节点状态
def update_node_status(status="running", pow_count=0):
    """更新节点状态信息"""
    try:
        current_time = time.time()
        node_info = {
            "status": status,
            "pow_count": pow_count,
            "last_seen": current_time,
            "heartbeat": current_time,
            "host": socket.gethostname(),
            "ip": NODE_IP
        }
        redis_client.hset(NODE_STATUS_KEY, mapping=node_info)
        logger.debug(f"已更新节点状态: {node_info}")
        return True
    except Exception as e:
        logger.error(f"更新节点状态时出错: {e}")
        return False

# 获取当前PoW结果数量
def get_pow_result_count():
    """获取当前PoW结果数量"""
    try:
        count = redis_client.llen(RedisKeys.POW_RESULT)
        return count
    except Exception as e:
        logger.error(f"获取PoW结果数量失败: {e}")
        return POW_MAX_COUNT  # 出错时假设已满，避免过度生成

# 保存PoW结果到Redis
def save_pow_result(token, cres, solve_time):
    """保存PoW结果到Redis"""
    try:
        # 创建结果对象，包含时间戳
        result = {
            "token": token,
            "cres": cres,
            "solve_time": solve_time,
            "created_time": time.time(),
            "node_id": NODE_IP  # 使用NODE_IP作为节点标识，而不是NODE_ID
        }
        
        # 将结果添加到Redis列表
        redis_client.rpush(RedisKeys.POW_RESULT, json.dumps(result))
        logger.info(f"已保存PoW结果，当前数量: {get_pow_result_count()}/{POW_MAX_COUNT}")
        return True
    except Exception as e:
        logger.error(f"保存PoW结果失败: {e}")
        return False

# 从Redis获取Token
def get_token_from_redis():
    """从Redis获取一个Token"""
    try:
        # 从Redis列表中弹出一个Token
        token_json = redis_client.lpop(RedisKeys.POW_TOKEN)
        
        if not token_json:
            logger.warning("Redis中没有可用的Token")
            return None
        
        # 解析Token
        token_obj = json.loads(token_json)
        
        # 检查Token是否已过期
        current_time = time.time()
        created_time = token_obj.get("created_time", 0)
        
        if current_time - created_time > POW_MAX_AGE:
            logger.warning(f"Token已过期，丢弃。创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(created_time))}")
            return None
        
        logger.info(f"成功获取Token: {token_obj.get('token')[:10]}..., 创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(created_time))}")
        return token_obj
    except Exception as e:
        logger.error(f"从Redis获取Token失败: {e}")
        return None

# 执行PoW挑战
def perform_pow_challenge():
    """执行一次PoW挑战，返回结果和消耗的时间"""
    logger.info("开始执行PoW挑战")
    
    try:
        # 从Redis获取Token
        token_obj = get_token_from_redis()
        if not token_obj:
            logger.error("无法获取Token，PoW挑战中止")
            return None, None, 0
        
        # 从token_obj中提取必要的参数
        token = token_obj.get("token")
        mask = token_obj.get("mask")
        key = token_obj.get("key")
        seed = token_obj.get("seed")
        create_time = token_obj.get("created_time")
        
        if not token or not mask or not key or not seed:
            logger.error("Token中缺少必要参数")
            return None, None, 0
        
        logger.info(f"开始计算PoW: token={token[:10]}..., seed={seed}, mask={mask}, key={key}")
        
        # 使用powlib执行PoW计算
        start_time = time.time()
        pow_result = powlib.solve_pow(seed=seed, mask=mask, prefix=key, threads=8, max_iterations=500000)
        
        end_time = time.time()
        solve_time = end_time - start_time
        
        # 检查结果
        if not pow_result or not pow_result.result:
            logger.error("PoW计算失败，未找到结果")
            return None, None, solve_time
        
        cres = pow_result.result
        iterations = pow_result.iterations
        
        logger.info(f"PoW计算成功: {cres}")
        logger.info(f"耗时: {solve_time:.2f}秒, 迭代次数: {iterations}")
        
        return token, cres, end_time - create_time
    except Exception as e:
        logger.error(f"执行PoW挑战过程中出错: {e}")
        return None, None, 0

# 主循环函数
def main_loop():
    """主循环：不断执行PoW计算并保存结果"""
    pow_count = 0
    
    # 更新节点状态为运行中
    update_node_status("running", pow_count)
    
    try:
        while True:
            # 检查当前PoW结果数量
            current_count = get_pow_result_count()
            
            if current_count >= POW_MAX_COUNT:
                logger.info(f"PoW结果数量已达上限 ({current_count}/{POW_MAX_COUNT})，等待...")
                time.sleep(5)
                continue
            
            # 执行PoW挑战
            token, cres, solve_time = perform_pow_challenge()
            
            # 检查结果
            if token and cres:
                # 检查求解时间是否超过限制
                if solve_time <= POW_MAX_SOLVE_TIME:
                    # 保存结果
                    if save_pow_result(token, cres, solve_time):
                        pow_count += 1
                        update_node_status("running", pow_count)
                else:
                    logger.warning(f"PoW求解时间过长 ({solve_time:.2f}秒 > {POW_MAX_SOLVE_TIME}秒)，丢弃结果")
            else:
                # 无法获取Token或计算失败，等待一段时间
                logger.warning("无法完成PoW计算，等待下一次尝试")
                # time.sleep(REQUEST_INTERVAL)
    
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
    except Exception as e:
        logger.error(f"主循环发生异常: {e}", exc_info=True)
    finally:
        # 更新节点状态为停止
        update_node_status("stopped", pow_count)
        logger.info(f"PoW工作线程已停止，共完成 {pow_count} 次PoW计算")

# 主函数
def main():
    """程序入口"""
    logger.info("启动PoW工作线程")
    logger.info(f"节点ID: {NODE_ID}")
    logger.info(f"节点IP: {NODE_IP}")  # 添加节点IP日志
    
    try:
        # 测试Redis连接
        redis_client.ping()
        logger.info(f"Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
        
        # 运行主循环
        main_loop()
    except redis.ConnectionError as e:
        logger.critical(f"Redis连接失败: {e}")
    except Exception as e:
        logger.critical(f"程序异常: {e}", exc_info=True)

if __name__ == "__main__":
    main() 