#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器监控脚本 - 用于监控分布式邮箱验证系统的运行状态
直接从ssh.json读取所有服务器信息，不依赖于部署状态
使用异步Redis客户端提高性能和稳定性
支持自动清理离线的节点
"""

import os
import time
import json
import asyncio
import datetime
import argparse
import logging
from prettytable import PrettyTable
from colorama import Fore, Back, Style, init
from dotenv import load_dotenv
import redis.asyncio as redis

# 加载环境变量
load_dotenv()

# 初始化colorama
init(autoreset=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# SSH配置文件路径
SSH_CONFIG_FILE = 'ssh.json'
OFFLINE_NODES_FILE = 'offline_nodes.txt'

# Redis配置
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

# 部署状态相关的Redis键
REDIS_PREFIX = "server:init:"  # 服务器初始化信息前缀
REDIS_SERVER_POOL = "server:pool"  # 存储可用服务器池
REDIS_SERVER_ASSIGNED = "server:assigned"  # 存储已分配服务器
REDIS_SERVER_FAILED = "server:failed"  # 存储连接失败的服务器
REDIS_SERVER_CHILDREN = "server:children"  # 存储服务器与子服务器的关系
REDIS_MASTER_NODES = "server:master:nodes"  # 主控节点集合

# 节点运行状态相关的Redis键 - 与main.py保持一致
NODE_PREFIX = "emails:node"  # 节点状态信息前缀（去掉末尾冒号，与email_verify.py保持一致）
POW_NODE_PREFIX = "pow:node"  # PoW节点状态信息前缀（去掉末尾冒号）
SYSTEM_STATS_SUFFIX = ":sys_info"  # 系统资源状态后缀（改为:sys_info，与email_verify.py一致）

# 禁用IP相关的Redis键
DISABLE_IP = "disable:ip"     # 禁用IP集合

# 邮箱处理相关的Redis键
EMAIL_QUEUE_KEY = "emails:queue"  # 待处理邮箱队列
SUCCESS_KEY = "emails:success"    # 成功邮箱集合
ERROR_KEY = "emails:error"        # 失败邮箱集合
PROCESSING_KEY = "emails:processing"  # 处理中邮箱集合
STATS_KEY = "emails:stats"        # 统计信息哈希表

# 最大重试次数
MAX_RETRIES = 3
# 最大并发连接数
MAX_CONCURRENT = int(os.getenv("MAX_CONCURRENT", 20))

# 已清理的节点列表
cleaned_nodes = set()

def load_ssh_config():
    """加载SSH配置文件"""
    try:
        with open(SSH_CONFIG_FILE, 'r') as f:
            config = json.load(f)
        logger.info(f"成功加载 {len(config)} 台服务器配置")
        return config
    except Exception as e:
        logger.error(f"加载SSH配置文件失败: {e}")
        return []

def save_ssh_config(config):
    """保存SSH配置文件"""
    try:
        with open(SSH_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"成功保存 {len(config)} 台服务器配置到 {SSH_CONFIG_FILE}")
        return True
    except Exception as e:
        logger.error(f"保存SSH配置文件失败: {e}")
        return False

def format_timestamp(timestamp):
    """将时间戳格式化为可读时间"""
    if not timestamp:
        return "-"
    try:
        timestamp = float(timestamp)
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, TypeError):
        return str(timestamp)

def format_elapsed_time(timestamp):
    """计算从时间戳到现在的经过时间"""
    if not timestamp:
        return "-"
    try:
        timestamp = float(timestamp)
        elapsed = time.time() - timestamp
        
        # 转换为小时:分钟:秒格式
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{int(seconds)}s"
    except (ValueError, TypeError):
        return "-"

def format_size(size_mb):
    """将MB格式化为更可读的大小"""
    try:
        size = float(size_mb)
        if size < 1000:
            return f"{size:.2f} MB"
        else:
            return f"{size/1024:.2f} GB"
    except (ValueError, TypeError):
        return str(size_mb)

async def save_offline_nodes(nodes):
    """保存离线节点IP到文件"""
    if not nodes:
        return
    
    try:
        # 追加模式，避免覆盖之前的记录
        with open(OFFLINE_NODES_FILE, 'a') as f:
            for node in nodes:
                f.write(f"{node}\n")
        logger.info(f"已将 {len(nodes)} 个离线节点保存到 {OFFLINE_NODES_FILE}")
    except Exception as e:
        logger.error(f"保存离线节点失败: {e}")

async def delete_node(redis_client, server_ip):
    """删除指定IP的节点所有相关数据"""
    try:
        # 检查节点是否存在
        server_key = f"{REDIS_PREFIX}{server_ip}"
        exists = await redis_client.exists(server_key)
        
        if not exists:
            logger.info(f"节点 {server_ip} 不存在初始化记录，但可能有其他记录")
        
        # 执行删除操作
        logger.info(f"正在删除节点 {server_ip} 的所有相关数据...")
        pipe = redis_client.pipeline()
        
        # 1. 从各种池中移除
        pipe.srem(REDIS_SERVER_ASSIGNED, server_ip)
        pipe.srem(REDIS_SERVER_FAILED, server_ip)
        pipe.srem(REDIS_MASTER_NODES, server_ip)
        pipe.srem(REDIS_SERVER_POOL, server_ip)
        
        # 2. 处理父子关系
        # 检查是否有父节点
        parent_key = f"{REDIS_SERVER_CHILDREN}:parent:{server_ip}"
        parent_ip = await redis_client.get(parent_key)
        
        if parent_ip:
            # 从父节点的子节点集合中移除
            pipe.srem(f"{REDIS_SERVER_CHILDREN}:{parent_ip}", server_ip)
            # 删除父子关系
            pipe.delete(parent_key)
            logger.debug(f"已从父节点 {parent_ip} 移除 {server_ip}")
        
        # 检查是否有子节点
        children_key = f"{REDIS_SERVER_CHILDREN}:{server_ip}"
        child_ips = await redis_client.smembers(children_key)
        
        if child_ips:
            for child_ip in child_ips:
                # 删除子节点指向父节点的关系
                pipe.delete(f"{REDIS_SERVER_CHILDREN}:parent:{child_ip}")
                logger.debug(f"已删除与子节点 {child_ip} 的关系")
            
            # 删除子节点集合
            pipe.delete(children_key)
        
        # 3. 删除节点状态信息
        pipe.delete(server_key)
        
        # 4. 删除节点运行状态（修改键格式，添加冒号分隔符）
        node_key = f"{NODE_PREFIX}:{server_ip}"
        stats_key = f"{node_key}{SYSTEM_STATS_SUFFIX}"
        pipe.delete(node_key)
        pipe.delete(stats_key)
        
        # 执行所有命令
        await pipe.execute()
        
        logger.info(f"已成功删除节点 {server_ip} 的所有相关数据")
        return True
        
    except Exception as e:
        logger.error(f"删除节点 {server_ip} 时出错: {e}")
        return False

async def remove_node_from_ssh_config(server_ip):
    """从SSH配置文件中移除指定IP的节点"""
    try:
        # 读取当前配置
        servers = load_ssh_config()
        if not servers:
            logger.error("无法加载SSH配置文件")
            return False
        
        # 查找并移除指定IP的服务器
        original_count = len(servers)
        servers = [server for server in servers if server.get('host') != server_ip]
        
        if len(servers) == original_count:
            logger.warning(f"在SSH配置中未找到节点 {server_ip}")
            return False
        
        # 保存更新后的配置
        if save_ssh_config(servers):
            logger.info(f"已从SSH配置中移除节点 {server_ip}")
            return True
        else:
            logger.error(f"保存SSH配置时出错，无法移除节点 {server_ip}")
            return False
        
    except Exception as e:
        logger.error(f"从SSH配置移除节点 {server_ip} 时出错: {e}")
        return False

async def cleanup_offline_nodes(redis_client, node_status):
    """直接清理离线节点"""
    global cleaned_nodes
    
    cleaned_in_this_run = []
    
    # 检查每个节点的状态
    for node_id, status in node_status.items():
        # 如果节点已被清理，跳过
        if node_id in cleaned_nodes:
            continue
            
        # 检查节点是否活跃
        is_active = check_node_active(status)
        
        if not is_active:
            logger.warning(f"检测到离线节点: {node_id}，立即清理")
            
            # 从Redis中删除
            if await delete_node(redis_client, node_id):
                # 从SSH配置文件中移除
                if await remove_node_from_ssh_config(node_id):
                    # 标记为已清理
                    cleaned_nodes.add(node_id)
                    cleaned_in_this_run.append(node_id)
    
    # 如果有新清理的节点，保存到文件
    if cleaned_in_this_run:
        await save_offline_nodes(cleaned_in_this_run)
        
    return len(cleaned_in_this_run)

def check_node_active(node_data):
    """检查节点是否活跃"""
    if not node_data or 'heartbeat' not in node_data:
        return False
    
    try:
        heartbeat = float(node_data['heartbeat'])
        now = time.time()
        # 如果心跳在3分钟内，认为是活跃的
        return (now - heartbeat) < 180
    except (ValueError, TypeError):
        return False

async def get_deploy_status(redis_client):
    """获取所有服务器的部署状态"""
    try:
        # 直接从ssh.json加载所有服务器
        ssh_servers = load_ssh_config()
        servers_status = {}
        
        # 创建并发任务获取每台服务器的状态
        async def get_server_status(server):
            try:
                server_ip = server['host']
                
                # 检查Redis中是否有该服务器的初始化信息
                server_key = f"{REDIS_PREFIX}{server_ip}"
                for retry in range(MAX_RETRIES):
                    try:
                        server_data = await redis_client.hgetall(server_key)
                        break
                    except Exception as e:
                        if retry == MAX_RETRIES - 1:
                            logger.error(f"获取服务器 {server_ip} 信息失败: {e}")
                            server_data = {}
                        else:
                            await asyncio.sleep(0.5)
                
                # 如果Redis中没有信息，创建一个基本记录
                if not server_data:
                    server_data = {
                        "status": "未初始化",
                        "host": server_ip,
                    }
                
                return server_ip, server_data
            except Exception as e:
                logger.error(f"处理服务器 {server.get('host', '未知')} 信息时出错: {e}")
                return None, None
        
        # 创建任务列表并等待所有任务完成
        tasks = [get_server_status(server) for server in ssh_servers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"获取服务器状态时发生异常: {result}")
                continue
                
            if result and result[0]:
                server_ip, server_data = result
                servers_status[server_ip] = server_data
        
        # 获取主控节点信息
        try:
            master_nodes = await redis_client.smembers(REDIS_MASTER_NODES)
        except Exception as e:
            logger.error(f"获取主控节点信息失败: {e}")
            master_nodes = set()
        
        return {
            'servers': servers_status,
            'master_nodes': master_nodes
        }
    except Exception as e:
        logger.error(f"获取部署状态失败: {e}")
        return {'servers': {}, 'master_nodes': set()}

async def get_running_status(redis_client):
    """获取所有节点的运行状态"""
    try:
        # 从SSH配置文件获取所有服务器
        ssh_servers = load_ssh_config()
        node_status = {}
        node_stats = {}
        pow_node_status = {}  # 存储PoW节点的状态
        
        # 获取禁用IP集合
        disabled_ips = set()
        try:
            disabled_ips = await redis_client.smembers(DISABLE_IP)
            logger.info(f"获取到 {len(disabled_ips)} 个禁用IP")
        except Exception as e:
            logger.error(f"获取禁用IP集合失败: {e}")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(MAX_CONCURRENT)
        
        # 获取单个节点状态的异步函数
        async def get_node_status(server):
            async with semaphore:
                try:
                    node_id = server['host']
                    
                    # 检查是否有节点状态信息 - 常规节点（添加冒号分隔符）
                    node_key = f"{NODE_PREFIX}:{node_id}"
                    stats_key = f"{node_key}{SYSTEM_STATS_SUFFIX}"
                    
                    # 检查是否有PoW节点状态信息（添加冒号分隔符）
                    pow_node_key = f"{POW_NODE_PREFIX}:{node_id}"
                    
                    # 获取节点基本状态
                    status_data = {}
                    try:
                        status_data = await redis_client.hgetall(node_key)
                    except Exception as e:
                        logger.debug(f"获取节点 {node_id} 状态失败: {e}，重试中...")
                        try:
                            await asyncio.sleep(0.5)
                            status_data = await redis_client.hgetall(node_key)
                        except Exception as e2:
                            logger.warning(f"重试获取节点 {node_id} 状态仍然失败: {e2}")
                    
                    # 获取PoW节点状态
                    pow_status_data = {}
                    try:
                        pow_status_data = await redis_client.hgetall(pow_node_key)
                    except Exception as e:
                        logger.debug(f"获取PoW节点 {node_id} 状态失败: {e}")
                    
                    # 获取节点系统资源统计信息
                    stats_data = {}
                    try:
                        stats_data = await redis_client.hgetall(stats_key)
                    except Exception as e:
                        logger.debug(f"获取节点 {node_id} 系统状态失败: {e}")

                    # 检查IP是否在禁用集合中
                    is_disabled = node_id in disabled_ips

                    # 如果没有任何状态信息，创建一个基本记录
                    if not status_data and not pow_status_data:
                        status_data = {
                            "status": "未运行",
                            "ip": node_id,
                            "host": server.get('hostname', node_id),
                            "is_disabled": is_disabled
                        }
                    else:
                        # 将禁用状态添加到现有节点数据中
                        if status_data:
                            status_data["is_disabled"] = is_disabled

                        if pow_status_data:
                            pow_status_data["is_disabled"] = is_disabled

                    return node_id, status_data, stats_data, pow_status_data
                except Exception as e:
                    logger.error(f"处理节点 {server.get('host', '未知')} 时出错: {e}")
                    return None, None, None, None
        
        # 并发获取所有节点状态
        tasks = [get_node_status(server) for server in ssh_servers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"获取节点状态时出错: {result}")
                continue

            if result and result[0]:
                node_id, status_data, stats_data, pow_status_data = result

                # 确保每个节点都被统计，即使没有运行状态
                # 如果有email节点状态数据，添加到node_status
                if status_data:
                    node_status[node_id] = status_data

                # 如果有系统统计数据，添加到node_stats
                if stats_data:
                    node_stats[node_id] = stats_data

                # 如果有PoW节点状态数据，添加到pow_node_status
                if pow_status_data:
                    pow_node_status[node_id] = pow_status_data
        
        return {
            'node_status': node_status,
            'node_stats': node_stats,
            'pow_node_status': pow_node_status,
            'disabled_ips': disabled_ips
        }
    except Exception as e:
        logger.error(f"获取运行状态失败: {e}")
        return {'node_status': {}, 'node_stats': {}, 'pow_node_status': {}, 'disabled_ips': set()}

async def get_email_stats(redis_client):
    """获取邮箱处理统计信息"""
    try:
        # 创建并发任务获取各种统计数据
        tasks = [
            redis_client.llen(EMAIL_QUEUE_KEY),
            redis_client.scard(SUCCESS_KEY),
            redis_client.scard(ERROR_KEY),
            redis_client.scard(PROCESSING_KEY),
            redis_client.hgetall(STATS_KEY)
        ]
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        queue_length = 0
        success_count = 0
        error_count = 0
        processing_count = 0
        stats = {}
        
        # 检查结果并处理异常
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"获取邮箱统计信息失败 (任务 {i}): {result}")
                continue
                
            if i == 0 and result is not None:
                queue_length = result
            elif i == 1 and result is not None:
                success_count = result
            elif i == 2 and result is not None:
                error_count = result
            elif i == 3 and result is not None:
                processing_count = result
            elif i == 4 and result is not None:
                stats = result
        
        # 计算派生统计数据
        total_processed = success_count + error_count
        total_emails = total_processed + queue_length + processing_count
        success_rate = (success_count / total_processed * 100) if total_processed > 0 else 0
        
        return {
            'queue_length': queue_length,
            'success_count': success_count,
            'error_count': error_count,
            'processing_count': processing_count,
            'total_processed': total_processed,
            'total_emails': total_emails,
            'success_rate': success_rate,
            'stats': stats
        }
    except Exception as e:
        logger.error(f"获取邮箱统计数据失败: {e}")
        return {
            'queue_length': 0,
            'success_count': 0,
            'error_count': 0,
            'processing_count': 0,
            'total_processed': 0,
            'total_emails': 0,
            'success_rate': 0,
            'stats': {}
        }

def print_status_tables(deploy_status, running_status, email_stats, cleaned_count=0):
    """打印状态表格"""
    # 清屏
    os.system('cls' if os.name == 'nt' else 'clear')
    
    print("\n" + "="*80)
    print(f"{Fore.CYAN}分布式邮箱验证系统监控{Style.RESET_ALL} - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 显示清理信息
    if cleaned_count > 0:
        print(f"\n{Fore.YELLOW}本次监控已清理 {Fore.RED}{cleaned_count} 个{Style.RESET_ALL} 离线节点")
    
    # 1. 打印服务器部署状态
    servers = deploy_status['servers']
    master_nodes = deploy_status['master_nodes']
    
    if servers:
        print(f"\n{Fore.YELLOW}服务器部署状态 ({len(servers)} 台):{Style.RESET_ALL}")
        
        server_table = PrettyTable()
        server_table.field_names = ["服务器IP", "状态", "部署时间", "最后更新", "角色"]
        server_table.align = "l"
        
        for server_ip, server_data in sorted(servers.items()):
            status = server_data.get('status', '未知')
            completed_time = format_timestamp(server_data.get('completed', '-'))
            last_updated = format_timestamp(server_data.get('last_updated', '-'))
            
            # 确定角色
            role = "👑 主控节点" if server_ip in master_nodes else "   子节点"
            
            # 设置状态颜色
            if status == 'completed':
                status_str = f"{Fore.GREEN}已完成{Style.RESET_ALL}"
            elif 'connected' in server_data:
                status_str = f"{Fore.YELLOW}{status}{Style.RESET_ALL}"
            else:
                status_str = f"{Fore.RED}{status}{Style.RESET_ALL}"
            
            server_table.add_row([
                server_ip,
                status_str,
                completed_time,
                last_updated,
                role
            ])
        
        print(server_table)
    else:
        print(f"\n{Fore.RED}未找到服务器配置{Style.RESET_ALL}")
    
    # 2. 打印节点运行状态
    node_status = running_status['node_status']
    pow_node_status = running_status['pow_node_status']
    node_stats = running_status['node_stats']
    disabled_ips = running_status['disabled_ips']
    
    email_nodes_count = len(node_status)
    pow_nodes_count = len(pow_node_status)
    total_nodes_count = email_nodes_count + pow_nodes_count
    
    # 打印禁用IP信息
    if disabled_ips:
        print(f"\n{Fore.RED}当前禁用IP ({len(disabled_ips)} 个): {', '.join(disabled_ips)}{Style.RESET_ALL}")
    
    if node_status or pow_node_status:
        print(f"\n{Fore.YELLOW}节点运行状态 (Email节点: {email_nodes_count}, PoW节点: {pow_nodes_count}, 总计: {total_nodes_count}):{Style.RESET_ALL}")
        
        node_table = PrettyTable()
        node_table.field_names = ["节点IP", "类型", "状态", "任务数", "心跳", "CPU使用", "内存使用", "磁盘使用", "网络传输", "禁用状态"]
        node_table.align = "l"
        
        # 合并显示Email节点和PoW节点
        all_nodes = set(node_status.keys()) | set(pow_node_status.keys())
        
        for node_id in sorted(all_nodes):
            # 获取Email节点状态
            status = node_status.get(node_id, {})
            # 获取PoW节点状态
            pow_status = pow_node_status.get(node_id, {})
            
            # 确定节点类型
            if node_id in node_status and node_id in pow_node_status:
                node_type = f"{Fore.GREEN}Email+PoW{Style.RESET_ALL}"
            elif node_id in node_status:
                node_type = f"{Fore.CYAN}Email{Style.RESET_ALL}"
            elif node_id in pow_status:
                node_type = f"{Fore.MAGENTA}PoW{Style.RESET_ALL}"
            else:
                node_type = "未知"
            
            # 首选Email节点的状态，如果没有则使用PoW节点的状态
            current_status = status if status else pow_status
            
            # 检查节点活跃状态
            is_active = check_node_active(current_status)
            node_status_value = current_status.get('status', 'unknown')
            
            # 设置状态颜色
            if is_active and node_status_value == 'running':
                status_str = f"{Fore.GREEN}运行中{Style.RESET_ALL}"
            elif is_active:
                status_str = f"{Fore.YELLOW}{node_status_value}{Style.RESET_ALL}"
            else:
                status_str = f"{Fore.RED}离线{Style.RESET_ALL}"
            
            # 获取心跳时间
            heartbeat_time = format_elapsed_time(current_status.get('heartbeat', '-'))
            
            # 获取系统资源信息
            stats = node_stats.get(node_id, {})
            cpu_percent = stats.get('cpu_percent', 'N/A')
            memory_percent = stats.get('memory_percent', 'N/A')
            disk_percent = stats.get('disk_percent', 'N/A')
            
            # 获取网络传输量
            net_sent = stats.get('net_sent_mb', 'N/A')
            net_recv = stats.get('net_recv_mb', 'N/A')
            if net_sent != 'N/A' and net_recv != 'N/A':
                net_usage = f"↑{format_size(net_sent)} ↓{format_size(net_recv)}"
            else:
                net_usage = 'N/A'
            
            # 检查IP是否被禁用
            is_disabled = node_id in disabled_ips
            disabled_status = f"{Fore.RED}已禁用{Style.RESET_ALL}" if is_disabled else f"{Fore.GREEN}正常{Style.RESET_ALL}"
            
            node_table.add_row([
                node_id,
                node_type,
                status_str,
                current_status.get('tasks', current_status.get('pow_count', '0')),
                heartbeat_time,
                f"{cpu_percent}" if cpu_percent != 'N/A' else 'N/A',
                f"{memory_percent}%" if memory_percent != 'N/A' else 'N/A',
                f"{disk_percent}%" if disk_percent != 'N/A' else 'N/A',
                net_usage,
                disabled_status
            ])
        
        print(node_table)
    else:
        print(f"\n{Fore.RED}未找到活跃的节点{Style.RESET_ALL}")
    
    # 3. 打印邮箱处理统计
    print(f"\n{Fore.YELLOW}邮箱处理统计:{Style.RESET_ALL}")
    
    stats_table = PrettyTable()
    stats_table.field_names = ["统计项", "数量"]
    stats_table.align = "l"
    
    success_rate = f"{email_stats['success_rate']:.2f}%"
    
    stats_table.add_row(["待处理邮箱", email_stats['queue_length']])
    stats_table.add_row(["处理中邮箱", email_stats['processing_count']])
    stats_table.add_row(["验证成功邮箱", email_stats['success_count']])
    stats_table.add_row(["验证失败邮箱", email_stats['error_count']])
    stats_table.add_row(["总处理邮箱", email_stats['total_processed']])
    stats_table.add_row(["总邮箱数量", email_stats['total_emails']])
    stats_table.add_row(["成功率", success_rate])
    
    print(stats_table)
    
    # 打印结束信息
    print("\n" + "="*80)
    print(f"Redis服务器: {REDIS_HOST}:{REDIS_PORT} | 按 Ctrl+C 停止监控")
    print("="*80)

async def monitor_async(refresh_interval=5, auto_cleanup=False):
    """异步监控系统状态"""
    redis_client = None
    try:
        # 连接Redis
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True,
            socket_connect_timeout=10.0,
            socket_timeout=30.0,
            retry_on_timeout=True,
            health_check_interval=15
        )
        
        # 测试Redis连接
        try:
            await redis_client.ping()
            logger.info(f"成功连接到Redis服务器: {REDIS_HOST}:{REDIS_PORT}")
        except Exception as e:
            logger.warning(f"无法连接到Redis服务器: {REDIS_HOST}:{REDIS_PORT}，部分功能可能不可用: {e}")
            # 尝试重新连接
            for i in range(MAX_RETRIES):
                try:
                    logger.info(f"正在尝试重新连接Redis（第{i+1}次尝试）...")
                    await asyncio.sleep(1)
                    await redis_client.ping()
                    logger.info("重新连接Redis成功")
                    break
                except Exception as e:
                    logger.warning(f"重新连接Redis失败: {e}")
                    if i == MAX_RETRIES - 1:
                        logger.error("多次尝试连接Redis失败，请检查Redis服务是否正常运行")
                        return
        
        # 检查SSH配置文件
        servers = load_ssh_config()
        if not servers:
            logger.error("无法加载SSH配置文件，请确保ssh.json存在并有效")
            return
            
        logger.info(f"开始监控系统状态，SSH服务器数量: {len(servers)}，刷新间隔: {refresh_interval}秒")
        
        if auto_cleanup:
            logger.info("已启用自动清理功能，将直接清理离线节点")
            
            # 初始化离线节点记录文件
            try:
                with open(OFFLINE_NODES_FILE, 'w') as f:
                    f.write(f"# 离线节点记录 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                logger.info(f"已初始化离线节点记录文件: {OFFLINE_NODES_FILE}")
            except Exception as e:
                logger.error(f"初始化离线节点记录文件失败: {e}")
        
        while True:
            try:
                # 收集所有状态信息 - 并行执行以提高性能
                tasks = [
                    get_deploy_status(redis_client),
                    get_running_status(redis_client),
                    get_email_stats(redis_client)
                ]
                
                deploy_status, running_status, email_stats = await asyncio.gather(*tasks)
            
                # 处理离线节点
                cleaned_count = 0
                if auto_cleanup:
                    cleaned_count = await cleanup_offline_nodes(redis_client, running_status['node_status'])
                
                # 打印状态表格
                print_status_tables(
                    deploy_status, running_status, email_stats, cleaned_count
                )
            
                # 等待刷新间隔
                await asyncio.sleep(refresh_interval)
            except Exception as e:
                logger.error(f"监控循环中发生错误: {e}")
                await asyncio.sleep(2)  # 出错后短暂等待再重试
            
    except KeyboardInterrupt:
        logger.info("监控已停止")
    except Exception as e:
        logger.error(f"监控异常: {e}")
    finally:
        # 关闭Redis连接
        if redis_client:
            try:
                await redis_client.aclose()
                logger.info("Redis连接已关闭")
            except Exception as e:
                logger.error(f"关闭Redis连接时出错: {e}")

async def main_async():
    """异步主函数"""
    global MAX_CONCURRENT
    
    parser = argparse.ArgumentParser(description='分布式邮箱验证系统监控工具')
    parser.add_argument('-i', '--interval', type=int, default=5, help='刷新间隔(秒)')
    parser.add_argument('-c', '--concurrent', type=int, help=f'最大并发请求数 (默认: {MAX_CONCURRENT})')
    parser.add_argument('--auto-cleanup', action='store_true', help='自动清理离线的节点')
    
    args = parser.parse_args()
    
    # 设置并发数
    if args.concurrent:
        MAX_CONCURRENT = args.concurrent
        logger.info(f"设置最大并发请求数: {MAX_CONCURRENT}")
    
    start_time = time.time()
    await monitor_async(
        refresh_interval=args.interval,
        auto_cleanup=args.auto_cleanup
    )
    elapsed = time.time() - start_time
    logger.info(f"监控程序运行了 {elapsed:.2f} 秒")

def main():
    """主函数入口"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()