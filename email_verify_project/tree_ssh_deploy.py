#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
树状SSH分发部署脚本
支持树状结构异步并发连接SSH服务器，执行命令和文件上传
使用Redis统一管理状态和任务分发
"""

import asyncio
import asyncssh
import json
import os
import sys
import time
import logging
import redis
import tarfile
import tempfile
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from config import REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD, setup_logger

# 设置日志
logger = setup_logger(__name__, "tree_ssh_deploy.log")

# Redis客户端
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=True
)

# Redis键名
SSH_TASK_QUEUE = "ssh:tasks"
SSH_NODE_STATUS = "ssh:nodes"
SSH_DEPLOY_STATUS = "ssh:deploy"

# 需要上传的文件列表
UPLOAD_FILES = [
    ".env", "config.py", "email_verify.py", "install.sh", 
    "node_utils.py", "pow_utils.py", "pow_worker.py", "powlib.so",
    "request_builder.py", "requirements.txt", "test10.py", "x64hash.py"
]

# 树状分发配置
MAX_CHILDREN = 5  # 每个节点最多连接5个子节点
DEPLOY_DIR = "/tmp/email_verify_deploy"  # 远程部署目录

class SSHNode:
    """SSH节点类"""
    
    def __init__(self, host: str, port: int, username: str, password: str, 
                 level: int = 0, parent: str = None):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.level = level
        self.parent = parent
        self.children = []
        self.connection = None
        self.status = "pending"  # pending, connecting, connected, failed
        
    def __str__(self):
        return f"SSHNode({self.host}:{self.port}, level={self.level})"
    
    async def connect(self):
        """连接SSH服务器"""
        try:
            logger.info(f"连接SSH服务器: {self.host}:{self.port}")
            self.status = "connecting"

            # 尝试多种SSH配置以提高兼容性
            connect_options = [
                # 现代配置
                {
                    'known_hosts': None,
                    'server_host_key_algs': ['rsa-sha2-512', 'rsa-sha2-256', 'ssh-rsa'],
                    'kex_algs': ['diffie-hellman-group14-sha256', 'diffie-hellman-group16-sha512'],
                    'encryption_algs': ['aes256-ctr', 'aes192-ctr', 'aes128-ctr'],
                    'mac_algs': ['hmac-sha2-256', 'hmac-sha2-512'],
                    'connect_timeout': 15
                },
                # 兼容性配置
                {
                    'known_hosts': None,
                    'server_host_key_algs': ['ssh-rsa', 'ssh-dss'],
                    'kex_algs': ['diffie-hellman-group14-sha1', 'diffie-hellman-group1-sha1'],
                    'encryption_algs': ['aes128-ctr', 'aes128-cbc', '3des-cbc'],
                    'mac_algs': ['hmac-sha1', 'hmac-md5'],
                    'connect_timeout': 15
                }
            ]

            last_error = None
            for i, options in enumerate(connect_options):
                try:
                    logger.info(f"尝试连接配置 {i+1}/{len(connect_options)}: {self.host}")
                    self.connection = await asyncssh.connect(
                        self.host,
                        port=self.port,
                        username=self.username,
                        password=self.password,
                        **options
                    )
                    break
                except Exception as e:
                    last_error = e
                    logger.warning(f"连接配置 {i+1} 失败: {e}")
                    continue

            if not self.connection:
                raise last_error or Exception("所有连接配置都失败")

            self.status = "connected"
            logger.info(f"成功连接到 {self.host}")

            # 更新Redis状态
            self.update_redis_status("connected")
            return True

        except Exception as e:
            self.status = "failed"
            logger.error(f"连接 {self.host} 失败: {e}")
            self.update_redis_status("failed", str(e))
            return False
    
    async def disconnect(self):
        """断开SSH连接"""
        if self.connection:
            self.connection.close()
            await self.connection.wait_closed()
            self.connection = None
            self.status = "disconnected"
            logger.info(f"断开连接: {self.host}")
    
    async def execute_command(self, command: str, timeout: int = 300) -> Dict[str, Any]:
        """执行SSH命令"""
        if not self.connection:
            return {"success": False, "error": "未连接"}
        
        try:
            logger.info(f"在 {self.host} 执行命令: {command}")
            result = await asyncio.wait_for(
                self.connection.run(command),
                timeout=timeout
            )
            
            output = {
                "success": True,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "exit_status": result.exit_status
            }
            
            if result.exit_status == 0:
                logger.info(f"命令执行成功: {self.host}")
            else:
                logger.warning(f"命令执行失败: {self.host}, 退出码: {result.exit_status}")
                
            return output
            
        except asyncio.TimeoutError:
            logger.error(f"命令执行超时: {self.host}")
            return {"success": False, "error": "执行超时"}
        except Exception as e:
            logger.error(f"命令执行异常: {self.host}, {e}")
            return {"success": False, "error": str(e)}
    
    async def upload_files(self, local_files: List[str], remote_dir: str) -> bool:
        """上传文件到远程服务器"""
        if not self.connection:
            logger.error(f"未连接到 {self.host}")
            return False
        
        try:
            # 创建远程目录
            await self.execute_command(f"mkdir -p {remote_dir}")
            
            # 创建临时tar包
            with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as tmp_file:
                tar_path = tmp_file.name
                
            with tarfile.open(tar_path, 'w:gz') as tar:
                for file_path in local_files:
                    if os.path.exists(file_path):
                        tar.add(file_path, arcname=os.path.basename(file_path))
                        logger.info(f"添加文件到tar包: {file_path}")
                    else:
                        logger.warning(f"文件不存在，跳过: {file_path}")
            
            # 上传tar包
            remote_tar = f"{remote_dir}/deploy.tar.gz"
            logger.info(f"上传文件包到 {self.host}:{remote_tar}")
            
            async with self.connection.start_sftp_client() as sftp:
                await sftp.put(tar_path, remote_tar)
            
            # 解压文件
            extract_cmd = f"cd {remote_dir} && tar -xzf deploy.tar.gz && rm deploy.tar.gz"
            result = await self.execute_command(extract_cmd)
            
            # 清理本地临时文件
            os.unlink(tar_path)
            
            if result["success"]:
                logger.info(f"文件上传成功: {self.host}")
                return True
            else:
                logger.error(f"文件解压失败: {self.host}")
                return False
                
        except Exception as e:
            logger.error(f"文件上传失败: {self.host}, {e}")
            return False
    
    def update_redis_status(self, status: str, error: str = None):
        """更新Redis中的节点状态"""
        try:
            node_data = {
                "host": self.host,
                "port": self.port,
                "level": self.level,
                "status": status,
                "last_update": time.time()
            }
            if error:
                node_data["error"] = error
            
            redis_client.hset(f"{SSH_NODE_STATUS}:{self.host}", mapping=node_data)
        except Exception as e:
            logger.error(f"更新Redis状态失败: {e}")

class TreeSSHManager:
    """树状SSH管理器"""
    
    def __init__(self, ssh_config_file: str = "ssh.json"):
        self.ssh_config_file = ssh_config_file
        self.nodes = []
        self.tree_structure = []
        
    def load_ssh_config(self) -> List[Dict]:
        """加载SSH配置文件"""
        try:
            with open(self.ssh_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"加载了 {len(config)} 个SSH服务器配置")
            return config
        except Exception as e:
            logger.error(f"加载SSH配置失败: {e}")
            return []
    
    def build_tree_structure(self, servers: List[Dict]) -> List[List[SSHNode]]:
        """构建树状结构"""
        if not servers:
            return []
        
        # 创建所有节点
        all_nodes = []
        for server in servers:
            node = SSHNode(
                host=server["host"],
                port=server.get("port", 22),
                username=server["username"],
                password=server["password"]
            )
            all_nodes.append(node)
        
        # 构建树状层级结构
        tree_levels = []
        remaining_nodes = all_nodes.copy()
        current_level = 0
        
        # 第一层：根节点（最多MAX_CHILDREN个）
        if remaining_nodes:
            root_nodes = remaining_nodes[:MAX_CHILDREN]
            for node in root_nodes:
                node.level = current_level
            tree_levels.append(root_nodes)
            remaining_nodes = remaining_nodes[MAX_CHILDREN:]
            current_level += 1
        
        # 后续层级：每个父节点最多连接MAX_CHILDREN个子节点
        while remaining_nodes:
            current_level_nodes = []
            parent_level = tree_levels[-1] if tree_levels else []
            
            for parent in parent_level:
                # 为每个父节点分配子节点
                children_count = min(MAX_CHILDREN, len(remaining_nodes))
                children = remaining_nodes[:children_count]
                
                for child in children:
                    child.level = current_level
                    child.parent = parent.host
                    parent.children.append(child)
                    current_level_nodes.append(child)
                
                remaining_nodes = remaining_nodes[children_count:]
                if not remaining_nodes:
                    break
            
            if current_level_nodes:
                tree_levels.append(current_level_nodes)
                current_level += 1
            else:
                break
        
        self.tree_structure = tree_levels
        self.nodes = all_nodes
        
        # 打印树状结构
        logger.info("树状结构构建完成:")
        for level, nodes in enumerate(tree_levels):
            logger.info(f"  第{level}层: {[str(node) for node in nodes]}")
        
        return tree_levels

    async def connect_level(self, level_nodes: List[SSHNode]) -> List[SSHNode]:
        """并发连接一层的所有节点"""
        logger.info(f"开始连接第{level_nodes[0].level if level_nodes else 0}层节点...")

        # 并发连接所有节点
        tasks = [node.connect() for node in level_nodes]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计连接结果
        connected_nodes = []
        for node, result in zip(level_nodes, results):
            if isinstance(result, Exception):
                logger.error(f"连接异常: {node.host}, {result}")
                node.status = "failed"
            elif result:
                connected_nodes.append(node)

        logger.info(f"第{level_nodes[0].level if level_nodes else 0}层连接完成: {len(connected_nodes)}/{len(level_nodes)}")
        return connected_nodes

    async def execute_on_level(self, level_nodes: List[SSHNode], command: str) -> Dict[str, Any]:
        """在一层的所有节点上执行命令"""
        logger.info(f"在第{level_nodes[0].level if level_nodes else 0}层执行命令: {command}")

        tasks = [node.execute_command(command) for node in level_nodes if node.status == "connected"]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 整理结果
        execution_results = {}
        for node, result in zip(level_nodes, results):
            if isinstance(result, Exception):
                execution_results[node.host] = {"success": False, "error": str(result)}
            else:
                execution_results[node.host] = result

        return execution_results

    async def upload_to_level(self, level_nodes: List[SSHNode], files: List[str], remote_dir: str) -> Dict[str, bool]:
        """向一层的所有节点上传文件"""
        logger.info(f"向第{level_nodes[0].level if level_nodes else 0}层上传文件...")

        tasks = [node.upload_files(files, remote_dir) for node in level_nodes if node.status == "connected"]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 整理结果
        upload_results = {}
        for node, result in zip(level_nodes, results):
            if isinstance(result, Exception):
                upload_results[node.host] = False
                logger.error(f"上传异常: {node.host}, {result}")
            else:
                upload_results[node.host] = result

        return upload_results

    async def deploy_project(self) -> bool:
        """部署项目到所有服务器"""
        logger.info("开始树状部署项目...")

        # 加载SSH配置
        servers = self.load_ssh_config()
        if not servers:
            logger.error("没有可用的SSH服务器配置")
            return False

        # 构建树状结构
        tree_levels = self.build_tree_structure(servers)
        if not tree_levels:
            logger.error("无法构建树状结构")
            return False

        # 检查本地文件
        local_files = []
        for file_name in UPLOAD_FILES:
            if os.path.exists(file_name):
                local_files.append(file_name)
            else:
                logger.warning(f"本地文件不存在: {file_name}")

        if not local_files:
            logger.error("没有找到任何需要上传的文件")
            return False

        logger.info(f"准备上传 {len(local_files)} 个文件: {local_files}")

        try:
            # 逐层连接和部署
            all_connected_nodes = []

            for level, level_nodes in enumerate(tree_levels):
                logger.info(f"处理第{level}层，共{len(level_nodes)}个节点")

                # 连接当前层节点
                connected_nodes = await self.connect_level(level_nodes)
                if not connected_nodes:
                    logger.error(f"第{level}层没有成功连接的节点")
                    continue

                all_connected_nodes.extend(connected_nodes)

                # 上传文件
                upload_results = await self.upload_to_level(connected_nodes, local_files, DEPLOY_DIR)
                success_count = sum(1 for success in upload_results.values() if success)
                logger.info(f"第{level}层文件上传完成: {success_count}/{len(connected_nodes)}")

                # 执行安装脚本
                install_results = await self.execute_on_level(connected_nodes, f"cd {DEPLOY_DIR} && chmod +x install.sh && ./install.sh")
                install_success = sum(1 for result in install_results.values() if result.get("success", False))
                logger.info(f"第{level}层安装完成: {install_success}/{len(connected_nodes)}")

                # 更新部署状态到Redis
                for node in connected_nodes:
                    deploy_status = {
                        "host": node.host,
                        "level": level,
                        "upload_success": upload_results.get(node.host, False),
                        "install_success": install_results.get(node.host, {}).get("success", False),
                        "deploy_time": time.time()
                    }
                    redis_client.hset(f"{SSH_DEPLOY_STATUS}:{node.host}", mapping=deploy_status)

            logger.info(f"部署完成，共连接 {len(all_connected_nodes)} 个节点")

            # 断开所有连接
            await self.disconnect_all(all_connected_nodes)

            return len(all_connected_nodes) > 0

        except Exception as e:
            logger.error(f"部署过程中发生异常: {e}")
            return False

    async def execute_command_tree(self, command: str) -> Dict[str, Any]:
        """在树状结构中执行命令"""
        logger.info(f"在树状结构中执行命令: {command}")

        # 加载配置并构建树
        servers = self.load_ssh_config()
        if not servers:
            return {}

        tree_levels = self.build_tree_structure(servers)
        if not tree_levels:
            return {}

        all_results = {}
        all_connected_nodes = []

        try:
            # 逐层连接和执行
            for level, level_nodes in enumerate(tree_levels):
                connected_nodes = await self.connect_level(level_nodes)
                if connected_nodes:
                    all_connected_nodes.extend(connected_nodes)
                    level_results = await self.execute_on_level(connected_nodes, command)
                    all_results.update(level_results)

            return all_results

        finally:
            # 断开所有连接
            await self.disconnect_all(all_connected_nodes)

    async def disconnect_all(self, nodes: List[SSHNode]):
        """断开所有SSH连接"""
        logger.info("断开所有SSH连接...")
        tasks = [node.disconnect() for node in nodes if node.connection]
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("所有连接已断开")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="树状SSH部署工具")
    parser.add_argument("action", choices=["deploy", "start", "stop", "restart", "status", "command"],
                       help="执行的操作")
    parser.add_argument("-c", "--command", help="自定义命令（当action为command时使用）")
    parser.add_argument("-f", "--config", default="ssh.json", help="SSH配置文件路径")

    args = parser.parse_args()

    # 创建SSH管理器
    manager = TreeSSHManager(args.config)

    try:
        if args.action == "deploy":
            logger.info("开始部署项目...")
            success = await manager.deploy_project()
            if success:
                logger.info("项目部署完成")
            else:
                logger.error("项目部署失败")
                sys.exit(1)

        elif args.action == "start":
            logger.info("启动服务...")
            results = await manager.execute_command_tree(f"cd {DEPLOY_DIR} && python3 email_verify.py &")
            print_results(results)

        elif args.action == "stop":
            logger.info("停止服务...")
            results = await manager.execute_command_tree("pkill -f email_verify.py")
            print_results(results)

        elif args.action == "restart":
            logger.info("重启服务...")
            # 先停止
            await manager.execute_command_tree("pkill -f email_verify.py")
            await asyncio.sleep(2)
            # 再启动
            results = await manager.execute_command_tree(f"cd {DEPLOY_DIR} && python3 email_verify.py &")
            print_results(results)

        elif args.action == "status":
            logger.info("检查服务状态...")
            results = await manager.execute_command_tree("ps aux | grep email_verify.py | grep -v grep")
            print_results(results)

        elif args.action == "command":
            if not args.command:
                logger.error("请使用 -c 参数指定要执行的命令")
                sys.exit(1)
            logger.info(f"执行自定义命令: {args.command}")
            results = await manager.execute_command_tree(args.command)
            print_results(results)

    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"执行过程中发生异常: {e}")
        sys.exit(1)

def print_results(results: Dict[str, Any]):
    """打印执行结果"""
    print("\n" + "="*50)
    print("执行结果:")
    print("="*50)

    for host, result in results.items():
        print(f"\n[{host}]")
        if result.get("success", False):
            print("✅ 执行成功")
            if result.get("stdout"):
                print(f"输出: {result['stdout'].strip()}")
        else:
            print("❌ 执行失败")
            if result.get("error"):
                print(f"错误: {result['error']}")
            if result.get("stderr"):
                print(f"错误输出: {result['stderr'].strip()}")

if __name__ == "__main__":
    asyncio.run(main())
