import base64
import json
import hashlib
import requests
import uuid
import time
import asyncio
from x64hash import x64hash128
import random
import ssl
import aiohttp

# URL常量
BASE_URL = "https://rat.rakuten.co.jp/"
LOGIN_URL = "https://login.account.rakuten.com/v2/login/start"
CLIENT_ID = "rakuten_ichiba_top_web"
REGISTRATION_URL = "https://login.account.rakuten.com/v2/registration"
REGISTRATION_CHECK_URL = "https://login.account.rakuten.com/v2/registration/1?dry_run=true"

# 默认超时设置（秒）
DEFAULT_TIMEOUT = 4

# 自定义用户代理列表
USER_AGENTS = [
    # Chrome
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    # Firefox
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/******** Firefox/120.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/******** Firefox/119.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/******** Firefox/120.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/******** Firefox/119.0",
    # Edge
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
    # Safari
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15"
]

# 获取随机 User-Agent
def get_random_user_agent():
    return random.choice(USER_AGENTS)

# 获取GC请求URL
def get_gc_url(tracking_id):
    return f"https://login.account.rakuten.com/util/gc?client_id={CLIENT_ID}&tracking_id={tracking_id}"

# 构造类似requests.Response的响应对象
class AsyncResponse:
    def __init__(self, status_code, text):
        self.status_code = status_code
        self.text = text

# 构造 rat.components 数据
def generate_rat_components_login():
    return {
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
        "language": "zh-CN",
        "color_depth": 24,
        "pixel_ratio": 1.5,
        "resolution": [1280, 720],
        "available_resolution": [1280, 672],
        "timezone_offset": -480,
        "session_storage": 1,
        "local_storage": 1,
        "indexed_db": 1,
        "cpu_class": "unknown",
        "navigator_platform": "Win32",
        "do_not_track": "unknown",
        "adblock": 0,
        "has_lied_languages": 0,
        "has_lied_resolution": 0,
        "has_lied_os": 0,
        "has_lied_browser": 0,
        "touch_support": [0, 0, 0]
    }

def generate_rat_components():
    fonts_pool = [
        "Calibri", "Franklin Gothic", "MS UI Gothic", "Marlett", "Segoe UI Light", "SimHei",
        "Arial", "Times New Roman", "Courier New", "Verdana", "Tahoma", "Comic Sans MS"
    ]
    fonts = random.sample(fonts_pool, k=random.randint(3, min(7, len(fonts_pool))))
    font_pref = {
        "default": random.uniform(90, 110),
        "apple": random.uniform(90, 110),
        "serif": random.uniform(80, 100),
        "sans": random.uniform(90, 110),
        "mono": random.uniform(70, 90),
        "min": random.uniform(5, 10),
        "system": random.uniform(100, 120)
    }
    audio_value = random.uniform(100, 150)
    screen_frame = [0, 0, random.choice([0, 40, 60]), 0]
    languages = [[random.choice(["zh-CN", "en-US", "ja-JP", "fr-FR", "de-DE"])] for _ in range(random.randint(1, 2))]
    color_depth = random.choice([8, 16, 24, 32])
    device_memory = random.choice([2, 4, 8, 16, 32])
    screen_res = [random.choice([720, 800, 900, 1080, 1440, 2160]), random.choice([1280, 1366, 1920, 2560, 3840])]
    hardware_conc = random.choice([2, 4, 8, 16, 32])
    timezone = random.choice(["Asia/Shanghai", "Asia/Tokyo", "Europe/Berlin", "America/New_York", "UTC"])
    session_storage = random.choice([True, False])
    local_storage = random.choice([True, False])
    indexed_db = random.choice([True, False])
    open_db = random.choice([True, False])
    platform = random.choice(["Win32", "Linux x86_64", "MacIntel"])
    plugins_pool = [
        {"name": "PDF Viewer", "description": "Portable Document Format", "mimeTypes": [{"type": "application/pdf", "suffixes": "pdf"}, {"type": "text/pdf", "suffixes": "pdf"}]},
        {"name": "Chrome PDF Viewer", "description": "Portable Document Format", "mimeTypes": [{"type": "application/pdf", "suffixes": "pdf"}]},
        {"name": "Microsoft Edge PDF Viewer", "description": "Portable Document Format", "mimeTypes": [{"type": "application/pdf", "suffixes": "pdf"}]},
        {"name": "WebKit built-in PDF", "description": "Portable Document Format", "mimeTypes": [{"type": "application/pdf", "suffixes": "pdf"}]},
        {"name": "Flash", "description": "Shockwave Flash", "mimeTypes": [{"type": "application/x-shockwave-flash", "suffixes": "swf"}]}
    ]
    plugins = random.sample(plugins_pool, k=random.randint(1, len(plugins_pool)))
    touch_support = {
        "maxTouchPoints": random.choice([0, 1, 2, 5, 10]),
        "touchEvent": random.choice([True, False]),
        "touchStart": random.choice([True, False])
    }
    vendor = random.choice(["Google Inc.", "Apple Inc.", "Mozilla Foundation"])
    vendor_flavors = random.sample(["chrome", "firefox", "safari", "edge"], k=random.randint(1, 2))
    color_gamut = random.choice(["srgb", "p3", "rec2020"])
    forced_colors = random.choice([True, False])
    monochrome = random.choice([0, 1, 2])
    contrast = random.choice([0, 1, 2, 3])
    reduced_motion = random.choice([True, False])
    hdr = random.choice([True, False])
    math_value = {k: random.uniform(0.1, 1000) for k in [
        "acos", "acosh", "acoshPf", "asin", "asinh", "asinhPf", "atanh", "atanhPf", "atan", "sin", "sinh", "sinhPf", "cos", "cosh", "coshPf", "tan", "tanh", "tanhPf", "exp", "expm1", "expm1Pf", "log1p", "log1pPf", "powPI"
    ]}
    video_card = {
        "vendor": random.choice(["Google Inc. (Intel)", "NVIDIA Corporation", "AMD Inc.", "Apple Inc."]),
        "renderer": random.choice([
            "ANGLE (Intel, Intel(R) UHD Graphics 620)",
            "ANGLE (NVIDIA, NVIDIA GeForce GTX 1050)",
            "ANGLE (AMD, AMD Radeon Pro 560X)",
            "ANGLE (Apple, Apple M1)"
        ])
    }
    architecture = random.choice([255, 64, 32])
    return {
        "fonts": {"value": fonts, "duration": random.randint(100, 200)},
        "domBlockers": {"duration": 0},
        "fontPreferences": {"value": font_pref, "duration": random.randint(10, 20)},
        "audio": {"value": audio_value, "duration": random.randint(1, 5)},
        "screenFrame": {"value": screen_frame, "duration": 0},
        "osCpu": {"duration": 0},
        "languages": {"value": languages, "duration": 0},
        "colorDepth": {"value": color_depth, "duration": 0},
        "deviceMemory": {"value": device_memory, "duration": 0},
        "screenResolution": {"value": screen_res, "duration": 0},
        "hardwareConcurrency": {"value": hardware_conc, "duration": 0},
        "timezone": {"value": timezone, "duration": 0},
        "sessionStorage": {"value": session_storage, "duration": 0},
        "localStorage": {"value": local_storage, "duration": 0},
        "indexedDB": {"value": indexed_db, "duration": 1},
        "openDatabase": {"value": open_db, "duration": 0},
        "cpuClass": {"duration": 0},
        "platform": {"value": platform, "duration": 0},
        "plugins": {"value": plugins, "duration": 0},
        "touchSupport": {"value": touch_support, "duration": 0},
        "vendor": {"value": vendor, "duration": 0},
        "vendorFlavors": {"value": vendor_flavors, "duration": 1},
        "cookiesEnabled": {"value": random.choice([True, False]), "duration": 1},
        "colorGamut": {"value": color_gamut, "duration": 0},
        "invertedColors": {"duration": 0},
        "forcedColors": {"value": forced_colors, "duration": 0},
        "monochrome": {"value": monochrome, "duration": 0},
        "contrast": {"value": contrast, "duration": 0},
        "reducedMotion": {"value": reduced_motion, "duration": 0},
        "hdr": {"value": hdr, "duration": 0},
        "math": {"value": math_value, "duration": 1},
        "videoCard": {"value": video_card, "duration": random.randint(50, 100)},
        "pdfViewerEnabled": {"value": True, "duration": 0},
        "architecture": {"value": architecture, "duration": 0}
    }

# 计算 rat.hash 的 MD5 哈希值
def calculate_hash(components):
    sorted_json = json.dumps(components, sort_keys=True)
    hash_object = hashlib.md5(sorted_json.encode('utf-8'))
    return hash_object.hexdigest()

# 构造完整请求体
def construct_payload():
    rat_components = generate_rat_components()
    rat_hash = calculate_hash(rat_components)
    
    return {
        # "page_type": "LOGIN_START",
        "page_type": "REGISTRATION_INPUT",
        "lang": "zh-CN",
        "rat": {
            "components": rat_components,
            "hash": rat_hash
        }
    }

def generate_rat_hash():
    rat_list = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        "zh-CN",
        24,
        1.5,
        "1280;720",
        "1280;672",
        -480,
        1,
        1,
        1,
        "unknown",
        "Win32",
        "unknown",
        0,
        0,
        0,
        0,
        0,
        "0;0;0;"
    ]

    rat_str = "~~~".join([str(i) for i in rat_list])
    return x64hash128(rat_str, 31)

# 发送异步 POST 请求
async def send_post_request_async(url, payload, timeout=DEFAULT_TIMEOUT, proxy=None):
    """使用 aiohttp 发送异步 POST 请求，带有随机浏览器指纹"""
    # 生成随机 User-Agent
    user_agent = get_random_user_agent()
    
    # 完整的浏览器请求头
    headers = {
        "Content-Type": "application/json",
        "User-Agent": user_agent,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Cache-Control": "max-age=0",
        "Sec-Ch-Ua": '"Chromium";v="120", "Google Chrome";v="120", "Not-A.Brand";v="8"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Pragma": "no-cache",
        "Origin": "https://login.account.rakuten.com",
        "Referer": "https://login.account.rakuten.com/sso/authorize"
    }
    
    try:
        # 创建自定义 SSL 上下文以绕过指纹识别
        ssl_context = ssl.create_default_context()
        
        # 配置超时
        timeout_obj = aiohttp.ClientTimeout(total=timeout)
        
        # 添加 Cookie 处理
        cookie_jar = aiohttp.CookieJar(unsafe=True)
        
        # 配置连接器，修复参数冲突
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        
        # 会话参数
        session_kwargs = {
            "timeout": timeout_obj,
            "connector": connector,
            "cookie_jar": cookie_jar
        }
        
        # 如果有代理，添加代理
        if proxy:
            print(f"使用代理: {proxy}")
            session_kwargs["proxy"] = proxy
        
        # 创建会话并发送请求
        async with aiohttp.ClientSession(**session_kwargs) as session:
            async with session.post(url, headers=headers, json=payload) as response:
                content = await response.text()
                # 返回 AsyncResponse，确保使用 response.status 作为 status_code 参数
                return AsyncResponse(response.status, content)
    
    except asyncio.TimeoutError:
        print(f"请求超时: {url}")
        return AsyncResponse(408, "Request Timeout")
    except Exception as e:
        print(f"请求异常: {url}, 错误: {str(e)}")
        return AsyncResponse(500, f"Request Error: {str(e)}")
    finally:
        await connector.close()

# 发送 POST 请求 (同步版本)
def send_post_request(url, payload, timeout=3, proxy=None):
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
    }
    if proxy:
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=timeout, proxies={"http": proxy, "https": proxy})
    else:
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=timeout)
    return response

# 构造login请求
def construct_payload_login(user_id, cres, token):
    return {
        "user_id": user_id,
        "type": None,
        "linkage_token": "",
        "without_sso": False,
        "authorize_request": {
            "client_id": "rakuten_ichiba_top_web",
            "redirect_uri": "https://www.rakuten.co.jp/",
            "scope": "openid",
            "response_type": "code",
            "ui_locales": "zh-CN",
            "state": "",
            "max_age": None,
            "nonce": "",
            "display": "page",
            "code_challenge": "",
            "code_challenge_method": "",
            "r10_required_claims": "",
            "r10_audience": "jid",
            "r10_jid_service_id": "omnit246",
            "r10_preferred_authentication": None,
            "r10_guest_login": False,
            "r10_disable_intra": True,
            "r10_force_account": None,
            "r10_own_scope": None,
            "r10_rejection": None,
            "token": None
        },
        "challenge": {
            "cres": cres,
            "token": token
        },
        "webauthn_supported": True
    }

# 生成随机的 tracking_id
def generate_tracking_id():
    return str(uuid.uuid4())

# 构造login语言
def construct_payload_login_lang():
    return {
        "authorize_request": {
            "client_id": "rakuten_ichiba_top_web",
            "redirect_uri": "https://www.rakuten.co.jp/",
            "scope": "openid",
            "response_type": "code",
            "ui_locales": "zh-CN",
            "state": "",
            "max_age": None,
            "nonce": "",
            "display": "page",
            "code_challenge": "",
            "code_challenge_method": "",
            "r10_required_claims": "",
            "r10_audience": "jid",
            "r10_jid_service_id": "omnit246",
            "r10_preferred_authentication": None,
            "r10_guest_login": False,
            "r10_disable_intra": True,
            "r10_force_account": None,
            "r10_own_scope": None,
            "r10_rejection": None,
            "token": None
        }
    }

def construct_cpkg_none_start(hash_code, tracking_id):
    current_time_ms = int(time.time() * 1000)
    return {
        "acc": "1249",
        "aid": 1,
        "cp": {
            "psx": current_time_ms,
            "his": "❮01❯",
            "s_m": "Main.Update",
            "s_f": "update",
            "f_p": f"\"{hash_code}\"",
            "f_f": [
                ["reenterEmailEnabled", True],
                ["reenterPasswordEnabled", True],
                ["enableTrustedDevice", False]
            ],
            "cid": "rakuten_ichiba_top_web",
            "cor": tracking_id,
            "x": 520,
            "y": 551,
            "coo": True,
            "l_s": True,
            "url": "https://login.account.rakuten.com/sso/authorize?client_id=rakuten_ichiba_top_web&redirect_uri=https://www.rakuten.co.jp/&ui_locales=zh-CN&scope=openid&response_type=code#/sign_in",
            "w_s": True,
            "lng": "zh-CN",
            "env": "production",
            "msg": "RequestChallomni,lastSeen:1",
            "evt": "ChallengerCore",
            "foc": False,
            "vis": True,
            "src": "/widget",
            "inf": "2.24.2-bf97-4bfd"
        }
    }

def construct_cpkg_none_initial(hash_code, tracking_id):
    current_time_ms = int(time.time() * 1000)
    return {
        "acc": "1249",
        "aid": 1,
        "cp": {
            "psx": current_time_ms,
            "his": "❮01❯",
            "s_m": "Challomni.Utils.elm",
            "s_f": "handleMessage",
            "f_p": f"\"{hash_code}\"",
            "f_f": [
                ["reenterEmailEnabled", True],
                ["reenterPasswordEnabled", True],
                ["enableTrustedDevice", False]
            ],
            "cid": "rakuten_ichiba_top_web",
            "cor": tracking_id,
            "x": 520,
            "y": 551,
            "coo": True,
            "l_s": True,
            "url": "https://login.account.rakuten.com/sso/authorize?client_id=rakuten_ichiba_top_web&redirect_uri=https://www.rakuten.co.jp/&ui_locales=zh-CN&scope=openid&response_type=code#/sign_in",
            "w_s": True,
            "lng": "zh-CN",
            "env": "production",
            "msg": "request,utilGc,lastSeen:1",
            "evt": "ChallengerRequestEvent",
            "foc": False,
            "vis": True,
            "src": "/widget",
            "inf": "2.24.2-bf97-4bfd"
        }
    }

def construct_cpkg_response_event(hash_code, tracking_id):
    current_time_ms = int(time.time() * 1000)
    return {
        "acc": "1249",
        "aid": 1,
        "cp": {
            "psx": current_time_ms,
            "his": "❮01❯",
            "s_m": "Challomni.Utils.elm",
            "s_f": "handleMessage",
            "f_p": f"\"{hash_code}\"",
            "f_f": [
                ["reenterEmailEnabled", True],
                ["reenterPasswordEnabled", True],
                ["enableTrustedDevice", False]
            ],
            "cid": "rakuten_ichiba_top_web",
            "cor": tracking_id,
            "x": 520,
            "y": 551,
            "coo": True,
            "l_s": True,
            "url": "https://login.account.rakuten.com/sso/authorize?client_id=rakuten_ichiba_top_web&redirect_uri=https://www.rakuten.co.jp/&ui_locales=zh-CN&scope=openid&response_type=code#/sign_in",
            "w_s": True,
            "lng": "zh-CN",
            "env": "production",
            "msg": "response,utilGc,200,lastSeen:1,lastSeenModel:1,ctype:POW",
            "evt": "ChallengerResponseEvent",
            "foc": True,
            "vis": True,
            "src": "/widget",
            "inf": "2.24.2-bf97-4bfd"
        }
    }

def construct_cpkg_started_event(hash_code, tracking_id):
    current_time_ms = int(time.time() * 1000)
    return {
        "acc": "1249",
        "aid": 1,
        "cp": {
            "psx": current_time_ms,
            "his": "❮01❯",
            "s_m": "Init",
            "s_f": "init_",
            "f_p": f"\"{hash_code}\"",
            "f_f": [
                ["reenterEmailEnabled", True],
                ["reenterPasswordEnabled", True],
                ["enableTrustedDevice", False]
            ],
            "cid": "rakuten_ichiba_top_web",
            "cor": tracking_id,
            "x": 520,
            "y": 551,
            "coo": True,
            "l_s": True,
            "url": "https://login.account.rakuten.com/sso/authorize?client_id=rakuten_ichiba_top_web&redirect_uri=https://www.rakuten.co.jp/&ui_locales=zh-CN&scope=openid&response_type=code#/sign_in",
            "w_s": False,
            "lng": "zh-CN",
            "env": "production",
            "msg": f"Main.elm started, cor:{tracking_id}, fp:\"{hash_code}\"",
            "evt": "StartedEvent",
            "foc": False,
            "vis": True,
            "src": "/widget",
            "inf": "2.24.2-bf97-4bfd"
        }
    }

def construct_cpkg_request_event(hash_code, tracking_id):
    current_time_ms = int(time.time() * 1000)
    return {
        "acc": "1249",
        "aid": 1,
        "cp": {
            "psx": current_time_ms,
            "his": "❮01❯",
            "s_m": "E85_wellknow_uiMetadata",
            "s_f": "request",
            "f_p": f"\"{hash_code}\"",
            "f_f": [
                ["reenterEmailEnabled", True],
                ["reenterPasswordEnabled", True],
                ["enableTrustedDevice", False]
            ],
            "cid": "rakuten_ichiba_top_web",
            "cor": tracking_id,
            "x": 520,
            "y": 551,
            "coo": True,
            "l_s": True,
            "url": "https://login.account.rakuten.com/sso/authorize?client_id=rakuten_ichiba_top_web&redirect_uri=https://www.rakuten.co.jp/&ui_locales=zh-CN&scope=openid&response_type=code#/sign_in",
            "w_s": True,
            "lng": "zh-CN",
            "env": "production",
            "msg": "request,UImetadata",
            "evt": "RequestEvent",
            "foc": False,
            "vis": True,
            "src": "/widget",
            "inf": "2.24.2-bf97-4bfd"
        }
    }

# 构造注册请求payload
def construct_payload_registration_request():
    """构造获取注册token的请求体"""
    return {
        "linkage_token": "",
        "authorize_request": {
            "client_id": "rakuten_ichiba_top_web",
            "redirect_uri": "https://www.rakuten.co.jp/",
            "scope": "openid",
            "response_type": "code",
            "ui_locales": "zh-CN",
            "state": "",
            "max_age": None,
            "nonce": "",
            "display": "page",
            "code_challenge": "",
            "code_challenge_method": "",
            "r10_required_claims": "",
            "r10_audience": "jid",
            "r10_jid_service_id": "omnit246",
            "r10_preferred_authentication": None,
            "r10_guest_login": False,
            "r10_disable_intra": True,
            "r10_force_account": None,
            "r10_own_scope": None,
            "r10_rejection": None,
            "token": None
        },
        "webauthn_supported": True
    }

# 构造注册验证请求体
def construct_payload_registration_check(email, cres, token, reg_token):
    """构造邮箱注册验证请求体"""
    return {
        "fields": [
            {
                "field_id": "email",
                "value": email
            },
            {
                "field_id": "username",
                "value": ""
            },
            {
                "field_id": "password",
                "value": "{6xi-awZ:WuBZD="
            },
            {
                "field_id": "last_name",
                "value": "Law"
            },
            {
                "field_id": "first_name",
                "value": "Stan"
            },
            {
                "field_id": "agreement",
                "value": "true"
            },
            {
                "field_id": "first_name_kana",
                "value": "ー"
            },
            {
                "field_id": "last_name_kana",
                "value": "ー"
            }
        ],
        "token": reg_token,
        "challenge": {
            "cres": cres,
            "token": token
        }
    }

# 异步获取注册token
async def get_registration_token_async(timeout=DEFAULT_TIMEOUT, proxy=None):
    """异步获取注册token"""
    url = REGISTRATION_URL
    payload = construct_payload_registration_request()
    response = await send_post_request_async(url, payload, timeout=timeout, proxy=proxy)
    
    if response.status_code == 200:
        try:
            result = json.loads(response.text)
            return result.get("token")
        except:
            print("解析注册token响应失败")
            return None
    else:
        print(f"获取注册token失败，状态码: {response.status_code}")
        return None

# 异步发送邮箱注册验证请求
async def verify_email_registration_async(email, cres, token, reg_token, timeout=DEFAULT_TIMEOUT, proxy=None):
    """异步发送邮箱注册验证请求"""
    url = REGISTRATION_CHECK_URL
    payload = construct_payload_registration_check(email, cres, token, reg_token)
    response = await send_post_request_async(url, payload, timeout=timeout, proxy=proxy)
    
    # 返回响应对象，由调用者处理验证结果
    return response 