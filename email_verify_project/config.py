import os
import logging
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# Redis配置
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost') 
REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
REDIS_DB = int(os.getenv('REDIS_DB', 0))
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)

# Redis键名统一管理
class RedisKeys:
    # 邮箱相关键名
    EMAIL_QUEUE = "emails:queue"                      # 待处理邮箱队列
    EMAIL_PROCESSING = "emails:processing"            # 处理中的邮箱集合
    EMAIL_SUCCESS = "emails:success"                  # 处理成功的邮箱集合
    EMAIL_ERROR = "emails:error"                      # 处理失败的邮箱集合
    EMAIL_UNCERTAIN = "emails:uncertain"              # 处理结果不确定的邮箱队列
    EMAIL_STATS = "emails:stats"                      # 统计信息哈希表
    
    # PoW相关键名
    POW_RESULT = "pow:results"                        # PoW结果列表
    POW_TOKEN = "pow:tokens"                          # Token列表
    
    # 节点相关键名
    NODE_PREFIX = "emails:node"                       # 节点信息前缀
    POW_NODE_PREFIX = "pow:node"                      # PoW节点信息前缀
    
    # 禁用IP集合
    DISABLE_IP = "disable:ip"                         # 禁用IP集合

# PoW配置
POW_MAX_AGE = int(os.getenv("POW_MAX_AGE", 80))       # PoW结果最大有效期（秒）
POW_TOKEN_MAX_COUNT = int(os.getenv("POW_TOKEN_MAX_COUNT", 100))  # Token队列最大长度
POW_MAX_COUNT = int(os.getenv("POW_MAX_COUNT", 1000))  # PoW结果最大数量
POW_MAX_SOLVE_TIME = int(os.getenv("POW_MAX_SOLVE_TIME", 90))  # PoW最大求解时间（秒）

# 网络配置
PROXY_URL = os.getenv("PROXY_URL", None)
REQUEST_INTERVAL = int(os.getenv("REQUEST_INTERVAL", 2))  # 请求间隔（秒）

# 节点配置
NODE_HEARTBEAT_TIMEOUT = 30  # 心跳超时时间（秒）

# 并发配置
MAX_CONCURRENT_TASKS = int(os.getenv("MAX_CONCURRENT_TASKS", 5))
MAX_CONCURRENT_TOKENS = int(os.getenv("MAX_CONCURRENT_TOKENS", 3))

# 日志配置
LOGS_FOLDER = "logs"

def setup_logger(name, log_filename):
    """
    设置日志记录器，不加时间戳，直接覆盖日志文件
    
    Args:
        name: 日志记录器名称
        log_filename: 日志文件名（不包含路径）
    
    Returns:
        配置好的日志记录器
    """
    # 确保日志目录存在
    os.makedirs(LOGS_FOLDER, exist_ok=True)
    
    # 构建完整的日志文件路径
    log_file = os.path.join(LOGS_FOLDER, log_filename)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 创建文件处理器（覆盖模式）
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式器（不包含时间戳）
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def setup_root_logger(log_filename):
    """
    设置根日志记录器，不加时间戳，直接覆盖日志文件
    
    Args:
        log_filename: 日志文件名（不包含路径）
    """
    # 确保日志目录存在
    os.makedirs(LOGS_FOLDER, exist_ok=True)
    
    # 构建完整的日志文件路径
    log_file = os.path.join(LOGS_FOLDER, log_filename)
    
    # 完全重置日志配置
    logging.shutdown()
    logging.root.handlers.clear()
    
    # 创建文件处理器（覆盖模式）
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式器（不包含时间戳）
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return log_file
