INFO - 日志系统初始化完成
INFO - 节点ID: node_b9227254_asus
INFO - 节点IP: ************
INFO - 节点状态键: emails:node:************
INFO - 主函数启动
INFO - 正在连接Redis: 127.0.0.1:6379
INFO - Redis连接成功: 127.0.0.1:6379
INFO - 启动token获取循环
INFO - 启动token获取，最大并发数: 10
INFO - 启动异步处理，最大并发任务数: 10
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
INFO - 已清理 10 个过期token
INFO - 获取到token: @St.ott-v2..., seed=1503983555, mask=2ac9, key=ac
INFO - 获取到token: @St.ott-v2..., seed=4029287648, mask=6f9b, key=d4
INFO - 已保存token到Redis，当前数量: 1/20000
INFO - 已保存token到Redis，当前数量: 2/20000
INFO - 获取到token: @St.ott-v2..., seed=2179844584, mask=fc0d, key=27
INFO - 获取到token: @St.ott-v2..., seed=3600993961, mask=eb0b, key=54
INFO - 已保存token到Redis，当前数量: 3/20000
INFO - 已保存token到Redis，当前数量: 3/20000
INFO - 获取到token: @St.ott-v2..., seed=2965707415, mask=4941, key=21
INFO - 已保存token到Redis，当前数量: 5/20000
INFO - 获取到token: @St.ott-v2..., seed=840420004, mask=6531, key=9c
INFO - 已保存token到Redis，当前数量: 6/20000
INFO - 获取到token: @St.ott-v2..., seed=2138563408, mask=5e73, key=94
INFO - 已保存token到Redis，当前数量: 7/20000
INFO - 获取到token: @St.ott-v2..., seed=1803523625, mask=7fdc, key=ac
INFO - 已保存token到Redis，当前数量: 8/20000
INFO - 获取到token: @St.ott-v2..., seed=1459901581, mask=8603, key=12
INFO - 已保存token到Redis，当前数量: 9/20000
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
WARNING - Redis中没有可用的PoW结果
INFO - 获取到token: @St.ott-v2..., seed=1384692656, mask=b787, key=55
INFO - 已保存token到Redis，当前数量: 10/20000
INFO - 所有任务已成功取消
ERROR - 关闭Redis连接时出错: 'Redis' object has no attribute 'aclose'
INFO - 程序被强制中断
