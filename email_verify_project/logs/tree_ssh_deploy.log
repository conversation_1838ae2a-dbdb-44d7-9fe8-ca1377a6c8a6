INFO - 执行自定义命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在树状结构中执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 加载了 39 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(192.140.169.89:22, level=0)', 'SSHNode(192.140.169.87:22, level=0)', 'SSHNode(192.140.169.86:22, level=0)', 'SSHNode(192.140.169.85:22, level=0)', 'SSHNode(192.140.169.84:22, level=0)']
INFO -   第1层: ['SSHNode(192.140.169.82:22, level=1)', 'SSHNode(192.140.169.81:22, level=1)', 'SSHNode(192.140.169.78:22, level=1)', 'SSHNode(192.140.169.77:22, level=1)', 'SSHNode(192.140.169.74:22, level=1)', 'SSHNode(192.140.169.73:22, level=1)', 'SSHNode(192.140.169.72:22, level=1)', 'SSHNode(192.140.169.71:22, level=1)', 'SSHNode(192.140.169.70:22, level=1)', 'SSHNode(192.140.169.68:22, level=1)', 'SSHNode(192.140.169.67:22, level=1)', 'SSHNode(192.140.169.90:22, level=1)', 'SSHNode(192.140.169.91:22, level=1)', 'SSHNode(192.140.169.92:22, level=1)', 'SSHNode(192.140.160.173:22, level=1)', 'SSHNode(192.140.160.167:22, level=1)', 'SSHNode(192.140.160.160:22, level=1)', 'SSHNode(192.140.160.158:22, level=1)', 'SSHNode(192.140.160.157:22, level=1)', 'SSHNode(192.140.160.156:22, level=1)', 'SSHNode(192.140.160.154:22, level=1)', 'SSHNode(192.140.160.152:22, level=1)', 'SSHNode(192.140.160.151:22, level=1)', 'SSHNode(192.140.160.143:22, level=1)', 'SSHNode(192.140.160.141:22, level=1)']
INFO -   第2层: ['SSHNode(192.140.160.126:22, level=2)', 'SSHNode(192.140.160.125:22, level=2)', 'SSHNode(192.140.160.123:22, level=2)', 'SSHNode(192.140.160.121:22, level=2)', 'SSHNode(192.140.160.118:22, level=2)', 'SSHNode(192.140.160.117:22, level=2)', 'SSHNode(192.140.160.115:22, level=2)', 'SSHNode(192.140.160.114:22, level=2)', 'SSHNode(192.140.160.113:22, level=2)']
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 192.140.169.89:22
INFO - 尝试连接配置 1/2: 192.140.169.89
INFO - 连接SSH服务器: 192.140.169.87:22
INFO - 尝试连接配置 1/2: 192.140.169.87
INFO - 连接SSH服务器: 192.140.169.86:22
INFO - 尝试连接配置 1/2: 192.140.169.86
INFO - 连接SSH服务器: 192.140.169.85:22
INFO - 尝试连接配置 1/2: 192.140.169.85
INFO - 连接SSH服务器: 192.140.169.84:22
INFO - 尝试连接配置 1/2: 192.140.169.84
INFO - 成功连接到 192.140.169.87
INFO - 成功连接到 192.140.169.84
INFO - 成功连接到 192.140.169.85
INFO - 成功连接到 192.140.169.86
WARNING - 连接配置 1 失败: Connection lost
INFO - 尝试连接配置 2/2: 192.140.169.89
WARNING - 连接配置 2 失败: Connection lost
ERROR - 连接 192.140.169.89 失败: Connection lost
INFO - 第0层连接完成: 4/5
INFO - 在第0层执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.87 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.86 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.85 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.84 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 命令执行成功: 192.140.169.87
INFO - 命令执行成功: 192.140.169.85
INFO - 命令执行成功: 192.140.169.84
INFO - 命令执行成功: 192.140.169.86
INFO - 开始连接第1层节点...
INFO - 连接SSH服务器: 192.140.169.82:22
INFO - 尝试连接配置 1/2: 192.140.169.82
INFO - 连接SSH服务器: 192.140.169.81:22
INFO - 尝试连接配置 1/2: 192.140.169.81
INFO - 连接SSH服务器: 192.140.169.78:22
INFO - 尝试连接配置 1/2: 192.140.169.78
INFO - 连接SSH服务器: 192.140.169.77:22
INFO - 尝试连接配置 1/2: 192.140.169.77
INFO - 连接SSH服务器: 192.140.169.74:22
INFO - 尝试连接配置 1/2: 192.140.169.74
INFO - 连接SSH服务器: 192.140.169.73:22
INFO - 尝试连接配置 1/2: 192.140.169.73
INFO - 连接SSH服务器: 192.140.169.72:22
INFO - 尝试连接配置 1/2: 192.140.169.72
INFO - 连接SSH服务器: 192.140.169.71:22
INFO - 尝试连接配置 1/2: 192.140.169.71
INFO - 连接SSH服务器: 192.140.169.70:22
INFO - 尝试连接配置 1/2: 192.140.169.70
INFO - 连接SSH服务器: 192.140.169.68:22
INFO - 尝试连接配置 1/2: 192.140.169.68
INFO - 连接SSH服务器: 192.140.169.67:22
INFO - 尝试连接配置 1/2: 192.140.169.67
INFO - 连接SSH服务器: 192.140.169.90:22
INFO - 尝试连接配置 1/2: 192.140.169.90
INFO - 连接SSH服务器: 192.140.169.91:22
INFO - 尝试连接配置 1/2: 192.140.169.91
INFO - 连接SSH服务器: 192.140.169.92:22
INFO - 尝试连接配置 1/2: 192.140.169.92
INFO - 连接SSH服务器: 192.140.160.173:22
INFO - 尝试连接配置 1/2: 192.140.160.173
INFO - 连接SSH服务器: 192.140.160.167:22
INFO - 尝试连接配置 1/2: 192.140.160.167
INFO - 连接SSH服务器: 192.140.160.160:22
INFO - 尝试连接配置 1/2: 192.140.160.160
INFO - 连接SSH服务器: 192.140.160.158:22
INFO - 尝试连接配置 1/2: 192.140.160.158
INFO - 连接SSH服务器: 192.140.160.157:22
INFO - 尝试连接配置 1/2: 192.140.160.157
INFO - 连接SSH服务器: 192.140.160.156:22
INFO - 尝试连接配置 1/2: 192.140.160.156
INFO - 连接SSH服务器: 192.140.160.154:22
INFO - 尝试连接配置 1/2: 192.140.160.154
INFO - 连接SSH服务器: 192.140.160.152:22
INFO - 尝试连接配置 1/2: 192.140.160.152
INFO - 连接SSH服务器: 192.140.160.151:22
INFO - 尝试连接配置 1/2: 192.140.160.151
INFO - 连接SSH服务器: 192.140.160.143:22
INFO - 尝试连接配置 1/2: 192.140.160.143
INFO - 连接SSH服务器: 192.140.160.141:22
INFO - 尝试连接配置 1/2: 192.140.160.141
INFO - 成功连接到 192.140.169.77
INFO - 成功连接到 192.140.169.74
INFO - 成功连接到 192.140.169.82
INFO - 成功连接到 192.140.169.78
INFO - 成功连接到 192.140.160.151
INFO - 成功连接到 192.140.160.152
INFO - 成功连接到 192.140.160.141
INFO - 成功连接到 192.140.169.70
INFO - 成功连接到 192.140.160.158
INFO - 成功连接到 192.140.169.92
INFO - 成功连接到 192.140.169.81
INFO - 成功连接到 192.140.160.157
INFO - 成功连接到 192.140.160.173
INFO - 成功连接到 192.140.169.72
INFO - 成功连接到 192.140.169.68
INFO - 成功连接到 192.140.160.156
INFO - 成功连接到 192.140.160.160
INFO - 成功连接到 192.140.160.154
INFO - 成功连接到 192.140.169.91
INFO - 成功连接到 192.140.160.143
INFO - 成功连接到 192.140.160.167
INFO - 成功连接到 192.140.169.71
INFO - 成功连接到 192.140.169.90
INFO - 成功连接到 192.140.169.73
INFO - 成功连接到 192.140.169.67
INFO - 第1层连接完成: 25/25
INFO - 在第1层执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.82 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.81 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.78 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.77 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.74 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.73 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.72 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.71 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.70 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.68 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.67 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.90 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.91 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.169.92 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.173 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.167 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.160 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.158 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.157 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.156 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.154 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.152 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.151 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.143 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.141 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 命令执行成功: 192.140.160.154
INFO - 命令执行成功: 192.140.160.156
INFO - 命令执行成功: 192.140.160.141
INFO - 命令执行成功: 192.140.160.151
INFO - 命令执行成功: 192.140.160.152
INFO - 命令执行成功: 192.140.160.143
INFO - 命令执行成功: 192.140.169.81
INFO - 命令执行成功: 192.140.160.173
INFO - 命令执行成功: 192.140.160.157
INFO - 命令执行成功: 192.140.160.158
INFO - 命令执行成功: 192.140.160.167
INFO - 命令执行成功: 192.140.169.92
INFO - 命令执行成功: 192.140.160.160
INFO - 命令执行成功: 192.140.169.91
INFO - 命令执行成功: 192.140.169.82
INFO - 命令执行成功: 192.140.169.74
INFO - 命令执行成功: 192.140.169.78
INFO - 命令执行成功: 192.140.169.90
INFO - 命令执行成功: 192.140.169.68
INFO - 命令执行成功: 192.140.169.72
INFO - 命令执行成功: 192.140.169.70
INFO - 命令执行成功: 192.140.169.77
INFO - 命令执行成功: 192.140.169.67
INFO - 命令执行成功: 192.140.169.71
INFO - 命令执行成功: 192.140.169.73
INFO - 开始连接第2层节点...
INFO - 连接SSH服务器: 192.140.160.126:22
INFO - 尝试连接配置 1/2: 192.140.160.126
INFO - 连接SSH服务器: 192.140.160.125:22
INFO - 尝试连接配置 1/2: 192.140.160.125
INFO - 连接SSH服务器: 192.140.160.123:22
INFO - 尝试连接配置 1/2: 192.140.160.123
INFO - 连接SSH服务器: 192.140.160.121:22
INFO - 尝试连接配置 1/2: 192.140.160.121
INFO - 连接SSH服务器: 192.140.160.118:22
INFO - 尝试连接配置 1/2: 192.140.160.118
INFO - 连接SSH服务器: 192.140.160.117:22
INFO - 尝试连接配置 1/2: 192.140.160.117
INFO - 连接SSH服务器: 192.140.160.115:22
INFO - 尝试连接配置 1/2: 192.140.160.115
INFO - 连接SSH服务器: 192.140.160.114:22
INFO - 尝试连接配置 1/2: 192.140.160.114
INFO - 连接SSH服务器: 192.140.160.113:22
INFO - 尝试连接配置 1/2: 192.140.160.113
INFO - 成功连接到 192.140.160.126
INFO - 成功连接到 192.140.160.125
INFO - 成功连接到 192.140.160.121
INFO - 成功连接到 192.140.160.118
INFO - 成功连接到 192.140.160.123
INFO - 成功连接到 192.140.160.117
INFO - 成功连接到 192.140.160.115
INFO - 成功连接到 192.140.160.114
INFO - 成功连接到 192.140.160.113
INFO - 第2层连接完成: 9/9
INFO - 在第2层执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.126 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.125 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.123 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.121 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.118 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.117 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.115 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.114 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
INFO - 在 192.140.160.113 执行命令: rm -rf ~/email_verify && echo 'Deleted ~/email_verify directory'
