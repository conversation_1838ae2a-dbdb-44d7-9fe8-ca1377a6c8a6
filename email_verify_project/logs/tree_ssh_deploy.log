INFO - 检查服务状态...
INFO - 在树状结构中执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 加载了 20 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(192.140.160.173:22, level=0)', 'SSHNode(192.140.160.167:22, level=0)', 'SSHNode(192.140.160.160:22, level=0)', 'SSHNode(192.140.160.158:22, level=0)', 'SSHNode(192.140.160.157:22, level=0)']
INFO -   第1层: ['SSHNode(192.140.160.156:22, level=1)', 'SSHNode(192.140.160.154:22, level=1)', 'SSHNode(192.140.160.152:22, level=1)', 'SSHNode(192.140.160.151:22, level=1)', 'SSHNode(192.140.160.143:22, level=1)', 'SSHNode(192.140.160.141:22, level=1)', 'SSHNode(192.140.160.126:22, level=1)', 'SSHNode(192.140.160.125:22, level=1)', 'SSHNode(192.140.160.123:22, level=1)', 'SSHNode(192.140.160.121:22, level=1)', 'SSHNode(192.140.160.118:22, level=1)', 'SSHNode(192.140.160.117:22, level=1)', 'SSHNode(192.140.160.115:22, level=1)', 'SSHNode(192.140.160.114:22, level=1)', 'SSHNode(192.140.160.113:22, level=1)']
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 192.140.160.173:22
INFO - 尝试连接配置 1/2: 192.140.160.173
INFO - 连接SSH服务器: 192.140.160.167:22
INFO - 尝试连接配置 1/2: 192.140.160.167
INFO - 连接SSH服务器: 192.140.160.160:22
INFO - 尝试连接配置 1/2: 192.140.160.160
INFO - 连接SSH服务器: 192.140.160.158:22
INFO - 尝试连接配置 1/2: 192.140.160.158
INFO - 连接SSH服务器: 192.140.160.157:22
INFO - 尝试连接配置 1/2: 192.140.160.157
INFO - 成功连接到 192.140.160.173
INFO - 成功连接到 192.140.160.160
INFO - 成功连接到 192.140.160.158
INFO - 成功连接到 192.140.160.157
INFO - 成功连接到 192.140.160.167
INFO - 第0层连接完成: 5/5
INFO - 在第0层执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.173 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.167 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.160 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.158 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.157 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 命令执行成功: 192.140.160.173
INFO - 命令执行成功: 192.140.160.160
INFO - 命令执行成功: 192.140.160.158
INFO - 命令执行成功: 192.140.160.167
INFO - 命令执行成功: 192.140.160.157
INFO - 开始连接第1层节点...
INFO - 连接SSH服务器: 192.140.160.156:22
INFO - 尝试连接配置 1/2: 192.140.160.156
INFO - 连接SSH服务器: 192.140.160.154:22
INFO - 尝试连接配置 1/2: 192.140.160.154
INFO - 连接SSH服务器: 192.140.160.152:22
INFO - 尝试连接配置 1/2: 192.140.160.152
INFO - 连接SSH服务器: 192.140.160.151:22
INFO - 尝试连接配置 1/2: 192.140.160.151
INFO - 连接SSH服务器: 192.140.160.143:22
INFO - 尝试连接配置 1/2: 192.140.160.143
INFO - 连接SSH服务器: 192.140.160.141:22
INFO - 尝试连接配置 1/2: 192.140.160.141
INFO - 连接SSH服务器: 192.140.160.126:22
INFO - 尝试连接配置 1/2: 192.140.160.126
INFO - 连接SSH服务器: 192.140.160.125:22
INFO - 尝试连接配置 1/2: 192.140.160.125
INFO - 连接SSH服务器: 192.140.160.123:22
INFO - 尝试连接配置 1/2: 192.140.160.123
INFO - 连接SSH服务器: 192.140.160.121:22
INFO - 尝试连接配置 1/2: 192.140.160.121
INFO - 连接SSH服务器: 192.140.160.118:22
INFO - 尝试连接配置 1/2: 192.140.160.118
INFO - 连接SSH服务器: 192.140.160.117:22
INFO - 尝试连接配置 1/2: 192.140.160.117
INFO - 连接SSH服务器: 192.140.160.115:22
INFO - 尝试连接配置 1/2: 192.140.160.115
INFO - 连接SSH服务器: 192.140.160.114:22
INFO - 尝试连接配置 1/2: 192.140.160.114
INFO - 连接SSH服务器: 192.140.160.113:22
INFO - 尝试连接配置 1/2: 192.140.160.113
INFO - 成功连接到 192.140.160.151
INFO - 成功连接到 192.140.160.152
INFO - 成功连接到 192.140.160.126
INFO - 成功连接到 192.140.160.154
INFO - 成功连接到 192.140.160.125
INFO - 成功连接到 192.140.160.143
INFO - 成功连接到 192.140.160.118
INFO - 成功连接到 192.140.160.123
INFO - 成功连接到 192.140.160.141
INFO - 成功连接到 192.140.160.156
INFO - 成功连接到 192.140.160.121
INFO - 成功连接到 192.140.160.113
INFO - 成功连接到 192.140.160.117
INFO - 成功连接到 192.140.160.114
INFO - 成功连接到 192.140.160.115
INFO - 第1层连接完成: 15/15
INFO - 在第1层执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.156 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.154 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.152 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.151 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.143 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.141 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.126 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.125 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.123 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.121 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.118 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.117 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.115 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.114 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 192.140.160.113 执行命令: cd ~/email_verify_deploy && ./status.sh
WARNING - 命令执行失败: 192.140.160.154, 退出码: 127
INFO - 命令执行成功: 192.140.160.126
INFO - 命令执行成功: 192.140.160.156
INFO - 命令执行成功: 192.140.160.151
INFO - 命令执行成功: 192.140.160.141
INFO - 命令执行成功: 192.140.160.152
INFO - 命令执行成功: 192.140.160.143
INFO - 命令执行成功: 192.140.160.118
INFO - 命令执行成功: 192.140.160.125
INFO - 命令执行成功: 192.140.160.123
INFO - 命令执行成功: 192.140.160.114
INFO - 命令执行成功: 192.140.160.113
INFO - 命令执行成功: 192.140.160.121
INFO - 命令执行成功: 192.140.160.117
INFO - 命令执行成功: 192.140.160.115
INFO - 断开所有SSH连接...
INFO - 断开连接: 192.140.160.173
INFO - 断开连接: 192.140.160.167
INFO - 断开连接: 192.140.160.160
INFO - 断开连接: 192.140.160.158
INFO - 断开连接: 192.140.160.157
INFO - 断开连接: 192.140.160.156
INFO - 断开连接: 192.140.160.154
INFO - 断开连接: 192.140.160.152
INFO - 断开连接: 192.140.160.151
INFO - 断开连接: 192.140.160.143
INFO - 断开连接: 192.140.160.141
INFO - 断开连接: 192.140.160.126
INFO - 断开连接: 192.140.160.125
INFO - 断开连接: 192.140.160.123
INFO - 断开连接: 192.140.160.121
INFO - 断开连接: 192.140.160.118
INFO - 断开连接: 192.140.160.117
INFO - 断开连接: 192.140.160.115
INFO - 断开连接: 192.140.160.114
INFO - 断开连接: 192.140.160.113
INFO - 所有连接已断开
