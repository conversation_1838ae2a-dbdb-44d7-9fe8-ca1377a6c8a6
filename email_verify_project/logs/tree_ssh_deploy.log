INFO - 启动服务...
INFO - 停止所有python3进程...
INFO - 在树状结构中执行命令: pkill -f python3 || true
INFO - 加载了 1 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(117.50.175.218:22, level=0)']
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 117.50.175.218:22
INFO - 尝试连接配置 1/2: 117.50.175.218
INFO - 成功连接到 117.50.175.218
INFO - 第0层连接完成: 1/1
INFO - 在第0层执行命令: pkill -f python3 || true
INFO - 在 117.50.175.218 执行命令: pkill -f python3 || true
WARNING - 命令执行失败: 117.50.175.218, 退出码: -1
INFO - 断开所有SSH连接...
INFO - 断开连接: 117.50.175.218
INFO - 所有连接已断开
INFO - 启动email_verify服务...
INFO - 在树状结构中执行命令: cd ~/email_verify_deploy && screen -dmS email_verify ./start_email.sh
INFO - 加载了 1 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(117.50.175.218:22, level=0)']
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 117.50.175.218:22
INFO - 尝试连接配置 1/2: 117.50.175.218
INFO - 成功连接到 117.50.175.218
INFO - 第0层连接完成: 1/1
INFO - 在第0层执行命令: cd ~/email_verify_deploy && screen -dmS email_verify ./start_email.sh
INFO - 在 117.50.175.218 执行命令: cd ~/email_verify_deploy && screen -dmS email_verify ./start_email.sh
INFO - 命令执行成功: 117.50.175.218
INFO - 断开所有SSH连接...
INFO - 断开连接: 117.50.175.218
INFO - 所有连接已断开
INFO - 启动pow_worker服务...
INFO - 在树状结构中执行命令: cd ~/email_verify_deploy && screen -dmS pow_worker ./start_pow.sh
INFO - 加载了 1 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(117.50.175.218:22, level=0)']
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 117.50.175.218:22
INFO - 尝试连接配置 1/2: 117.50.175.218
INFO - 成功连接到 117.50.175.218
INFO - 第0层连接完成: 1/1
INFO - 在第0层执行命令: cd ~/email_verify_deploy && screen -dmS pow_worker ./start_pow.sh
INFO - 在 117.50.175.218 执行命令: cd ~/email_verify_deploy && screen -dmS pow_worker ./start_pow.sh
INFO - 命令执行成功: 117.50.175.218
INFO - 断开所有SSH连接...
INFO - 断开连接: 117.50.175.218
INFO - 所有连接已断开
INFO - email_verify.py 启动结果:
INFO - pow_worker.py 启动结果:
INFO - 检查启动状态...
INFO - 在树状结构中执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 加载了 1 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(117.50.175.218:22, level=0)']
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 117.50.175.218:22
INFO - 尝试连接配置 1/2: 117.50.175.218
INFO - 成功连接到 117.50.175.218
INFO - 第0层连接完成: 1/1
INFO - 在第0层执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 在 117.50.175.218 执行命令: cd ~/email_verify_deploy && ./status.sh
INFO - 命令执行成功: 117.50.175.218
INFO - 断开所有SSH连接...
INFO - 断开连接: 117.50.175.218
INFO - 所有连接已断开
