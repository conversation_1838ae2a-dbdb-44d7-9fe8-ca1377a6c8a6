INFO - 开始部署项目...
INFO - 开始树状部署项目...
INFO - 加载了 1 个SSH服务器配置
INFO - 树状结构构建完成:
INFO -   第0层: ['SSHNode(117.50.175.218:22, level=0)']
INFO - 准备上传 12 个文件: ['.env', 'config.py', 'email_verify.py', 'install.sh', 'node_utils.py', 'pow_utils.py', 'pow_worker.py', 'powlib.so', 'request_builder.py', 'requirements.txt', 'test10.py', 'x64hash.py']
INFO - 处理第0层，共1个节点
INFO - 开始连接第0层节点...
INFO - 连接SSH服务器: 117.50.175.218:22
INFO - 尝试连接配置 1/2: 117.50.175.218
INFO - 成功连接到 117.50.175.218
INFO - 第0层连接完成: 1/1
INFO - 向第0层上传文件...
INFO - 在 117.50.175.218 执行命令: echo $HOME
INFO - 命令执行成功: 117.50.175.218
INFO - 展开路径: /home/<USER>/email_verify_deploy
INFO - 在 117.50.175.218 执行命令: mkdir -p /home/<USER>/email_verify_deploy
INFO - 命令执行成功: 117.50.175.218
INFO - 添加文件到tar包: .env
INFO - 添加文件到tar包: config.py
INFO - 添加文件到tar包: email_verify.py
INFO - 添加文件到tar包: install.sh
INFO - 添加文件到tar包: node_utils.py
INFO - 添加文件到tar包: pow_utils.py
INFO - 添加文件到tar包: pow_worker.py
INFO - 添加文件到tar包: powlib.so
INFO - 添加文件到tar包: request_builder.py
INFO - 添加文件到tar包: requirements.txt
INFO - 添加文件到tar包: test10.py
INFO - 添加文件到tar包: x64hash.py
INFO - 上传文件包到 117.50.175.218:/home/<USER>/email_verify_deploy/deploy.tar.gz
INFO - 在 117.50.175.218 执行命令: cd /home/<USER>/email_verify_deploy && tar -xzf deploy.tar.gz && rm deploy.tar.gz
INFO - 命令执行成功: 117.50.175.218
INFO - 文件上传成功: 117.50.175.218
INFO - 第0层文件上传完成: 1/1
INFO - 在第0层执行命令: cd ~/email_verify_deploy && chmod +x install.sh && ./install.sh
INFO - 在 117.50.175.218 执行命令: cd ~/email_verify_deploy && chmod +x install.sh && ./install.sh
