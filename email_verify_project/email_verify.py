import json
import asyncio
import logging
import time
import socket
import psutil
import redis.asyncio as redis
import redis as redis_sync
from redis.exceptions import RedisError

# 导入配置和工具模块
from config import (
    REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD,
    RedisKeys, POW_MAX_AGE, POW_TOKEN_MAX_COUNT, PROXY_URL,
    NODE_HEARTBEAT_TIMEOUT, MAX_CONCURRENT_TASKS, MAX_CONCURRENT_TOKENS,
    setup_root_logger
)
from node_utils import get_node_id, get_node_ip

# 获取节点信息
NODE_ID = get_node_id()
NODE_IP = get_node_ip()

# 使用NODE_IP构建节点状态键
NODE_STATUS_KEY = f"{RedisKeys.NODE_PREFIX}:{NODE_IP}"

# 设置日志
log_file = setup_root_logger("email_verify.log")

# 输出测试日志
print(f"Logger initialized, writing to {log_file}")
logging.info("日志系统初始化完成")
logging.info(f"节点ID: {NODE_ID}")
logging.info(f"节点IP: {NODE_IP}")
logging.info(f"节点状态键: {NODE_STATUS_KEY}")

# 初始化Redis同步客户端，用于获取PoW结果
redis_sync_client = redis_sync.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=True
)

# 使用带重试机制的Redis函数
async def redis_operation(func, *args, retries=3, delay=1, **kwargs):
    """执行Redis操作，带重试机制"""
    for attempt in range(retries):
        try:
            return await func(*args, **kwargs)
        except (RedisError, ConnectionError) as e:
            if attempt == retries - 1:
                # 最后一次尝试，记录错误并重新引发
                logging.error(f"Redis操作失败 (达到最大重试次数 {retries}): {e}")
                raise
            # 等待一段时间后重试
            wait_time = delay * (2 ** attempt)  # 指数退避
            logging.warning(f"Redis操作失败，将在 {wait_time:.1f} 秒后重试 (尝试 {attempt+1}/{retries}): {e}")
            await asyncio.sleep(wait_time)

# 更新节点状态
async def update_node_status(redis_client, status="running", email_count=0, successful=0, failed=0, uncertain=0):
    """更新节点状态信息到Redis"""
    try:
        current_time = time.time()
        node_info = {
            "status": status,
            "emails_processed": email_count,
            "successful": successful,
            "failed": failed, 
            "uncertain": uncertain,
            "last_seen": current_time,
            "heartbeat": current_time,
            "host": socket.gethostname(),
            "ip": NODE_IP
        }
        await redis_operation(redis_client.hset, NODE_STATUS_KEY, mapping=node_info)
        return True
    except Exception as e:
        logging.error(f"更新节点状态失败: {e}")
        return False

# 定期心跳任务
async def heartbeat_task(redis_client):
    """周期性更新节点心跳信息"""
    while True:
        try:
            current_time = time.time()
            
            # 更新心跳时间
            await redis_operation(redis_client.hset, NODE_STATUS_KEY, "heartbeat", current_time)
            
            # 检查并处理超时的处理中邮箱
            await check_processing_timeouts(redis_client)
            
            logging.debug(f"节点心跳已更新: {current_time}")
        except Exception as e:
            logging.error(f"更新心跳失败: {e}")
        
        # 等待下一次心跳
        await asyncio.sleep(10)

# 检查并处理超时的处理中邮箱
async def check_processing_timeouts(redis_client):
    """检查处理中队列，将超时的邮箱移回待处理队列，使用与main.py一致的集合操作"""
    try:
        current_time = time.time()
        
        # 获取所有处理中的邮箱（使用SMEMBERS而非HGETALL）
        processing_emails = await redis_operation(redis_client.smembers, RedisKeys.EMAIL_PROCESSING)
        
        for email in processing_emails:
            # 由于在集合中只存储了邮箱而非其他数据，我们只能检查节点心跳
            # 检查本节点心跳是否超时
            node_heartbeat = await redis_operation(redis_client.hget, NODE_STATUS_KEY, "heartbeat")
            
            if node_heartbeat and current_time - float(node_heartbeat) <= NODE_HEARTBEAT_TIMEOUT:
                # 节点仍活跃，不处理
                continue
            
            # 本节点已不活跃或心跳超时，将邮箱放回队列
            logging.warning(f"检测到超时邮箱处理: {email}")
            
            # 使用pipeline执行操作
            pipe = redis_client.pipeline()
            
            # 从处理中集合移除
            pipe.srem(RedisKeys.EMAIL_PROCESSING, email)

            # 放回待处理队列
            pipe.rpush(RedisKeys.EMAIL_QUEUE, email)

            # 增加不确定结果计数
            pipe.hincrby(RedisKeys.EMAIL_STATS, "uncertain", 1)
            
            # 执行pipeline
            await redis_operation(pipe.execute)
            
            logging.info(f"将超时邮箱放回队列: {email}")
            
    except Exception as e:
        logging.error(f"检查处理中邮箱超时异常: {e}")

# 系统信息监控
async def system_monitor_task(redis_client):
    """周期性收集和更新系统信息"""
    while True:
        try:
            # 收集系统信息
            sys_info = {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "update_time": time.time()
            }
            
            # 更新到Redis
            await redis_operation(redis_client.hset, f"{NODE_STATUS_KEY}:sys_info", mapping=sys_info)
            logging.debug(f"系统信息已更新: {sys_info}")
        except Exception as e:
            logging.error(f"更新系统信息失败: {e}")
        
        # 等待下一次更新
        await asyncio.sleep(60)  # 每分钟更新一次

# 获取PoW结果
def get_pow_result():
    """从Redis获取一个可用的PoW结果"""
    try:
        # 获取当前时间
        current_time = time.time()
        
        # 直接从列表中获取一个结果
        result_json = redis_sync_client.lpop(RedisKeys.POW_RESULT)
        if not result_json:
            logging.warning("Redis中没有可用的PoW结果")
            return None
            
        result = json.loads(result_json)
        
        # 检查结果是否超时
        created_time = result.get("created_time", 0)
        if current_time - created_time > POW_MAX_AGE:
            # 超时的结果，丢弃
            logging.debug(f"丢弃过期的PoW结果: 创建时间={created_time}, 当前时间={current_time}")
            return None
        
        logging.info(f"获取到可用的PoW结果，token: {result.get('token')[:10]}..., 创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result.get('created_time')))}")
        return result
        
    except Exception as e:
        logging.error(f"获取PoW结果失败: {e}")
        return None

# 从队列获取待处理邮箱
async def get_email_from_queue(redis_client):
    """从待处理队列获取一个邮箱，确保与main.py中使用相同的数据格式和类型"""
    try:
        # 使用LPOP从列表中弹出一个邮箱（字符串形式，非JSON）
        email = await redis_operation(redis_client.lpop, RedisKeys.EMAIL_QUEUE)

        if not email:
            return None

        # 将邮箱添加到处理中集合（使用集合而非哈希表）
        await redis_operation(redis_client.sadd, RedisKeys.EMAIL_PROCESSING, email)
        
        # 创建包含必要信息的邮箱数据对象
        email_data = {
            "email": email,
            "node_id": NODE_ID,
            "start_time": time.time(),
            "retries": 0
        }
        
        logging.info(f"获取到待处理邮箱: {email}")
        return email_data
    except Exception as e:
        logging.error(f"从队列获取邮箱失败: {e}")
        return None

# 标记邮箱处理成功
async def mark_email_success(redis_client, email_data, verify_result=None):
    """将邮箱标记为处理成功，使用与main.py一致的处理方式"""
    try:
        email = email_data.get("email")
        
        # 使用pipeline执行多个操作
        pipe = redis_client.pipeline()
        
        # 从处理中集合移除（使用SREM而非HDEL）
        pipe.srem(RedisKeys.EMAIL_PROCESSING, email)

        # 添加到成功集合（使用SADD而非HSET）
        pipe.sadd(RedisKeys.EMAIL_SUCCESS, email)

        # 增加成功计数
        pipe.hincrby(RedisKeys.EMAIL_STATS, "success", 1)
        
        # 执行pipeline
        await redis_operation(pipe.execute)
        
        logging.info(f"邮箱验证成功: {email}")
        return True
    except Exception as e:
        logging.error(f"标记邮箱成功失败: {e}")
        return False

# 标记邮箱处理失败
async def mark_email_error(redis_client, email_data, error_reason):
    """将邮箱标记为处理失败，使用与main.py一致的处理方式"""
    try:
        email = email_data.get("email")
        
        # 使用pipeline执行多个操作
        pipe = redis_client.pipeline()
        
        # 从处理中集合移除（使用SREM而非HDEL）
        pipe.srem(RedisKeys.EMAIL_PROCESSING, email)

        # 添加到失败集合（使用SADD而非HSET）
        pipe.sadd(RedisKeys.EMAIL_ERROR, email)

        # 增加失败计数
        pipe.hincrby(RedisKeys.EMAIL_STATS, "error", 1)
        
        # 执行pipeline
        await redis_operation(pipe.execute)
        
        logging.info(f"邮箱验证失败: {email}, 原因: {error_reason}")
        return True
    except Exception as e:
        logging.error(f"标记邮箱失败时出错: {e}")
        return False

# 将邮箱放回不确定队列
async def return_to_uncertain_queue(redis_client, email_data, reason="verification_uncertainty"):
    """将邮箱放回不确定队列，使用与main.py一致的处理方式"""
    try:
        email = email_data.get("email")
        
        # 使用pipeline执行多个操作
        pipe = redis_client.pipeline()
        
        # 从处理中集合移除（使用SREM而非HDEL）
        pipe.srem(RedisKeys.EMAIL_PROCESSING, email)

        # 放回队列前端（高优先级）
        pipe.rpush(RedisKeys.EMAIL_QUEUE, email)

        # 增加不确定结果计数
        pipe.hincrby(RedisKeys.EMAIL_STATS, "uncertain", 1)
        
        # 执行pipeline
        await redis_operation(pipe.execute)
        
        logging.info(f"邮箱放回队列: {email}, 原因: {reason}")
        return True
    except Exception as e:
        logging.error(f"将邮箱放回队列失败: {e}")
        return False

# 验证邮箱
async def verify_email(redis_client, email_data, pow_result=None):
    """验证邮箱有效性"""
    try:
        email = email_data.get("email")
        logging.info(f"开始验证邮箱: {email}")
        
        # 如果没有传入pow_result，则尝试获取一个
        if not pow_result:
            pow_result = get_pow_result()
            
            if not pow_result:
                logging.warning(f"无法获取PoW结果，邮箱验证中止: {email}")
                return await return_to_uncertain_queue(redis_client, email_data, reason="no_pow_result_available")
        
        token = pow_result.get("token")
        cres = pow_result.get("cres")
        
        if not token or not cres:
            logging.error(f"PoW结果缺少必要字段，邮箱验证中止: {email}")
            return await return_to_uncertain_queue(redis_client, email_data, reason="invalid_pow_result")
        
        # 使用request_builder构造请求并发送
        try:
            import request_builder
            
            # 首先，获取注册token
            logging.info(f"正在获取注册token: {email}")
            reg_token = await request_builder.get_registration_token_async(proxy=PROXY_URL, timeout=5)
            
            if not reg_token:
                logging.error(f"无法获取注册token，邮箱验证中止: {email}")
                return await return_to_uncertain_queue(redis_client, email_data, reason="failed_to_get_registration_token")
            
            logging.info(f"成功获取注册token: {email}, token前缀: {reg_token[:10]}...")
            
            # 发送邮箱注册验证请求
            logging.info(f"发送邮箱注册验证请求: {email}")
            response = await request_builder.verify_email_registration_async(
                email=email,
                cres=cres,
                token=token,
                reg_token=reg_token,
                proxy=PROXY_URL,
                timeout=5
            )
            
            # 生成响应对象
            verify_result = {
                "status_code": response.status_code,
                "response": response.text[:200],  # 只保存前200个字符
                "request_time": time.time()
            }
            
            # 解析响应判断结果
            if response.status_code == 200:
                # 状态码200表示邮箱未注册，验证失败
                verify_result["success"] = False
                verify_result["error"] = "email_not_registered"
                error_data = json.loads(response.text)
                logging.info(f"error_data: {error_data}")
                logging.info(f"邮箱验证失败 (邮箱未注册): {email}")
                await mark_email_error(redis_client, email_data, "email_not_registered")
                return False
            elif response.status_code == 400:
                # 解析400错误内容
                try:
                    error_data = json.loads(response.text)
                    if (error_data.get("errorCode") == "VALIDATION_ERROR" and 
                            any(err.get("reasonCode") == "ALREADY_EXISTS" and err.get("field") == "email" 
                                for err in error_data.get("errors", []))):
                        # 发现ALREADY_EXISTS错误，表示邮箱已注册
                        verify_result["success"] = True
                        logging.info(f"邮箱验证成功 (邮箱已注册): {email}")
                        await mark_email_success(redis_client, email_data, verify_result)
                        return True
                    else:
                        # 其他400错误
                        logging.warning(f"邮箱验证结果不确定 (未知400错误): {email}, 内容: {error_data}")
                        await return_to_uncertain_queue(redis_client, email_data, reason=f"unknown_400_error: {error_data.get('errorCode', 'unknown')}")
                        return None
                except json.JSONDecodeError as e:
                    logging.error(f"解析响应JSON失败: {e}")
                    await return_to_uncertain_queue(redis_client, email_data, reason="json_parse_error")
                    return None
            else:
                # 其他状态码
                logging.warning(f"邮箱验证结果不确定: {email}, 状态码: {response.status_code}")
                await return_to_uncertain_queue(redis_client, email_data, reason=f"uncertain_response_{response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"验证请求发送失败: {e}")
            await return_to_uncertain_queue(redis_client, email_data, reason=f"request_error: {str(e)}")
            return None
        
    except Exception as e:
        logging.error(f"邮箱验证过程中出错: {e}")
        await return_to_uncertain_queue(redis_client, email_data, reason=f"verification_error: {str(e)}")
        return None

# 任务处理函数
async def process_email_task(redis_client, semaphore, stats):
    """处理单个邮箱的任务"""
    async with semaphore:
        try:
            # 先获取一个有效的PoW结果
            pow_result = get_pow_result()
            if not pow_result:
                logging.debug("没有可用的PoW结果，等待...")
                await asyncio.sleep(1)
                return False
            
            token = pow_result.get("token", "")
            token_prefix = token[:8] + "..." if token else "无"
            
            # 有可用的PoW结果，尝试获取邮箱
            email_data = await get_email_from_queue(redis_client)
            
            if not email_data:
                # 队列为空，等待一段时间
                logging.debug(f"邮箱队列为空，已获取PoW结果(token前缀: {token_prefix})将被丢弃...")
                await asyncio.sleep(2)
                return False
            
            email = email_data.get("email", "")
            logging.info(f"开始处理邮箱 [{email}] 使用PoW结果(token前缀: {token_prefix})")
            
            # 验证邮箱，传入获取到的PoW结果
            result = await verify_email(redis_client, email_data, pow_result)
            
            # 更新计数
            if result is True:
                stats["successful"] += 1
                logging.info(f"邮箱 [{email}] 验证成功，当前成功数: {stats['successful']}")
            elif result is False:
                stats["failed"] += 1
                logging.info(f"邮箱 [{email}] 验证失败，当前失败数: {stats['failed']}")
            else:
                stats["uncertain"] += 1
                logging.info(f"邮箱 [{email}] 验证结果不确定，当前不确定数: {stats['uncertain']}")
            
            stats["email_count"] += 1
            
            # 更新节点状态
            await update_node_status(
                redis_client, 
                status="running", 
                email_count=stats["email_count"],
                successful=stats["successful"],
                failed=stats["failed"],
                uncertain=stats["uncertain"]
            )
            
            return True
        except Exception as e:
            logging.error(f"处理邮箱任务异常: {e}")
            await asyncio.sleep(1)
            return False

# 主循环函数
async def main_loop(redis_client):
    """主循环：使用异步并发模式处理邮箱验证"""
    # 统计信息
    stats = {
        "email_count": 0,
        "successful": 0,
        "failed": 0,
        "uncertain": 0
    }
    
    # 更新节点状态为运行中
    await update_node_status(redis_client, status="running")
    
    # 创建信号量，控制并发任务数量
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)

    logging.info(f"启动异步处理，最大并发任务数: {MAX_CONCURRENT_TASKS}")
    
    while True:
        try:
            # 创建任务列表
            tasks = []
            for _ in range(MAX_CONCURRENT_TASKS):
                task = asyncio.create_task(process_email_task(redis_client, semaphore, stats))
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks)
            
            # 短暂休息，避免CPU过度使用
            # await asyncio.sleep(0.5)
        except Exception as e:
            logging.error(f"主循环执行异常: {e}")
            await asyncio.sleep(5)  # 异常情况下等待时间更长

# 检查IP是否被禁用
async def is_ip_disabled(redis_client, ip_address):
    """检查IP是否在禁用集合中"""
    try:
        is_member = await redis_operation(redis_client.sismember, RedisKeys.DISABLE_IP, ip_address)
        return is_member
    except Exception as e:
        logging.error(f"检查IP禁用状态失败: {e}")
        return False

# 记录IP到禁用集合
async def add_ip_to_disabled(redis_client, ip_address):
    """记录IP到禁用集合"""
    try:
        await redis_operation(redis_client.sadd, RedisKeys.DISABLE_IP, ip_address)
        logging.warning(f"已将IP添加到禁用集合: {ip_address}")
        return True
    except Exception as e:
        logging.error(f"添加IP到禁用集合失败: {e}")
        return False

# 获取token的函数
async def get_token():
    """获取token和挑战参数"""
    try:
        import request_builder
        
        # 生成tracking_id
        tracking_id = request_builder.generate_tracking_id()
        # 获取gc_url
        gc_url = request_builder.get_gc_url(tracking_id)
        # 构造payload
        payload = request_builder.construct_payload()
        
        # 异步发送请求获取token和挑战参数
        response = await request_builder.send_post_request_async(gc_url, payload, proxy=PROXY_URL, timeout=5)
        
        if response.status_code != 200:
            logging.error(f"获取token失败，状态码: {response.status_code}")
            return None
        
        # 解析响应
        try:
            result = json.loads(response.text)
            token = result.get("token")
            if not token:
                logging.error("响应中没有token字段")
                return None
            
            # 解析挑战参数
            mdata = json.loads(result.get("mdata"))
            
            # 获取PoW参数
            mask = mdata.get("body").get("mask")
            key = mdata.get("body").get("key")
            seed = mdata.get("body").get("seed")
            
            # 创建token对象
            token_obj = {
                "token": token,
                "mask": mask,
                "key": key,
                "seed": seed,
                "tracking_id": tracking_id,
                "created_time": time.time()
            }
            
            # 检查mask长度
            if len(mask) > 5:
                logging.error(f"检测到mask大于5: {mask}，将记录节点IP到禁用集合")
                # 使用当前节点的IP地址
                return {"special": True, "ip": NODE_IP}

            logging.info(f"获取到token: {token[:10]}..., seed={seed}, mask={mask}, key={key}")
            return token_obj
        except Exception as e:
            logging.error(f"解析token响应失败: {e}")
            return None
    except Exception as e:
        logging.error(f"获取token过程中出错: {e}")
        return None

# 将token保存到Redis
async def save_token_to_redis(redis_client, token_obj):
    """保存token到Redis"""
    try:
        # 检查是否是特殊标记（检测到特殊mask）
        if isinstance(token_obj, dict) and token_obj.get("special"):
            # 记录IP到禁用集合
            ip_address = token_obj.get("ip")
            await add_ip_to_disabled(redis_client, ip_address)
            logging.warning(f"已将IP {ip_address} 添加到禁用集合，后续将停止获取token")
            return True
        
        # 检查当前队列长度
        current_count = await redis_operation(redis_client.llen, RedisKeys.POW_TOKEN)
        
        if current_count >= POW_TOKEN_MAX_COUNT:
            logging.warning(f"Token队列已满 ({current_count}/{POW_TOKEN_MAX_COUNT})，丢弃新token")
            return False
        
        # 将token添加到Redis列表
        await redis_operation(redis_client.rpush, RedisKeys.POW_TOKEN, json.dumps(token_obj))
        logging.info(f"已保存token到Redis，当前数量: {current_count + 1}/{POW_TOKEN_MAX_COUNT}")
        return True
    except Exception as e:
        logging.error(f"保存token到Redis失败: {e}")
        return False

# 异步获取token的任务
async def token_fetcher_task(redis_client, semaphore):
    """异步获取token并保存到Redis"""
    async with semaphore:
        try:
            # 检查当前IP是否在禁用集合中
            if await is_ip_disabled(redis_client, NODE_IP):
                logging.debug(f"当前IP {NODE_IP} 在禁用集合中，停止获取token")
                await asyncio.sleep(5)  # 如果禁用了，等待更长时间
                return False
            
            # 检查当前token队列长度
            current_count = await redis_operation(redis_client.llen, RedisKeys.POW_TOKEN)
            
            if current_count >= POW_TOKEN_MAX_COUNT:
                logging.debug(f"Token队列已满 ({current_count}/{POW_TOKEN_MAX_COUNT})，等待...")
                await asyncio.sleep(1)
                return False
            
            # 获取token
            token_obj = await get_token()
            
            if token_obj:
                # 保存到Redis
                success = await save_token_to_redis(redis_client, token_obj)
                return success
            else:
                logging.warning("获取token失败")
                await asyncio.sleep(2)  # 失败后等待时间更长
                return False
        except Exception as e:
            logging.error(f"获取token任务异常: {e}")
            await asyncio.sleep(2)
            return False

# 管理token获取的主循环
async def token_fetcher_loop(redis_client):
    """管理token获取的主循环"""
    logging.info("启动token获取循环")
    
    # 创建信号量，控制并发任务数量
    token_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TOKENS)

    logging.info(f"启动token获取，最大并发数: {MAX_CONCURRENT_TOKENS}")
    
    while True:
        try:
            # 检查当前IP是否在禁用集合中
            if await is_ip_disabled(redis_client, NODE_IP):
                logging.info(f"当前IP {NODE_IP} 在禁用集合中，暂停token获取")
                await asyncio.sleep(5)
                continue
            
            # 检查当前token队列长度
            current_count = await redis_operation(redis_client.llen, RedisKeys.POW_TOKEN)
            
            if current_count >= POW_TOKEN_MAX_COUNT:
                logging.debug(f"Token队列已满 ({current_count}/{POW_TOKEN_MAX_COUNT})，暂停获取")
                await asyncio.sleep(5)
                continue
            
            # 计算需要获取的token数量
            tokens_to_fetch = min(MAX_CONCURRENT_TOKENS, POW_TOKEN_MAX_COUNT - current_count)
            
            if tokens_to_fetch > 0:
                logging.debug(f"准备获取 {tokens_to_fetch} 个token，当前数量: {current_count}/{POW_TOKEN_MAX_COUNT}")
                
                # 创建任务列表
                tasks = []
                for _ in range(tokens_to_fetch):
                    task = asyncio.create_task(token_fetcher_task(redis_client, token_semaphore))
                    tasks.append(task)
                
                # 等待所有任务完成
                await asyncio.gather(*tasks)
            
            # 短暂休息
            await asyncio.sleep(1)
        except Exception as e:
            logging.error(f"Token获取循环异常: {e}")
            await asyncio.sleep(5)

# 清理过期的token
async def cleanup_expired_tokens(redis_client):
    """定期清理过期的token"""
    while True:
        try:
            # 获取所有token
            all_tokens = await redis_operation(redis_client.lrange, RedisKeys.POW_TOKEN, 0, -1)
            current_time = time.time()
            expired_count = 0
            
            for token_json in all_tokens:
                try:
                    token_obj = json.loads(token_json)
                    created_time = token_obj.get("created_time", 0)
                    
                    # 检查是否过期
                    if current_time - created_time > POW_MAX_AGE:
                        # 从列表中移除过期token
                        await redis_operation(redis_client.lrem, RedisKeys.POW_TOKEN, 1, token_json)
                        expired_count += 1
                except Exception as e:
                    logging.error(f"处理token时出错: {e}")
            
            if expired_count > 0:
                logging.info(f"已清理 {expired_count} 个过期token")
            
            # 每60秒检查一次
            await asyncio.sleep(60)
        except Exception as e:
            logging.error(f"清理过期token异常: {e}")
            await asyncio.sleep(60)

# 主函数
async def main():
    """程序入口"""
    redis_client = None
    tasks = []
    
    try:
        print("Starting main function...")
        logging.info("主函数启动")
        
        # 创建异步Redis客户端
        print(f"Connecting to Redis at {REDIS_HOST}:{REDIS_PORT}...")
        logging.info(f"正在连接Redis: {REDIS_HOST}:{REDIS_PORT}")
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        
        # 测试Redis连接
        try:
            print("Testing Redis connection...")
            await asyncio.wait_for(redis_client.ping(), timeout=5)
            print("Redis connection successful!")
            logging.info(f"Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
        except asyncio.TimeoutError:
            print(f"Redis connection timeout after 5 seconds")
            logging.error(f"Redis连接超时: {REDIS_HOST}:{REDIS_PORT}")
            return
        except Exception as e:
            print(f"Redis connection error: {str(e)}")
            logging.error(f"Redis连接错误: {str(e)}")
            return
        
        # 更新节点状态为正在启动
        print("Updating node status to 'starting'...")
        await update_node_status(redis_client, status="starting")
        
        # 启动各个任务
        print("Starting tasks...")
        tasks = [
            asyncio.create_task(heartbeat_task(redis_client)),
            asyncio.create_task(system_monitor_task(redis_client)),
            asyncio.create_task(token_fetcher_loop(redis_client)),  # 新增token获取任务
            asyncio.create_task(cleanup_expired_tokens(redis_client)),  # 新增token清理任务
            asyncio.create_task(main_loop(redis_client))
        ]
        
        print(f"Created {len(tasks)} tasks")
        
        # 等待所有任务完成
        print("Waiting for tasks to complete...")
        done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
        
        print(f"Task completed: {len(done)} done, {len(pending)} pending")
        
        # 如果有任务完成，打印异常（如果有）
        for task in done:
            try:
                task.result()
            except Exception as e:
                print(f"Task error: {str(e)}")
                logging.error(f"任务异常结束: {e}")
        
    except KeyboardInterrupt:
        print("Keyboard interrupt received")
        logging.info("收到中断信号，正在优雅退出...")
    except Exception as e:
        print(f"Main function error: {str(e)}")
        logging.critical(f"程序运行异常: {e}", exc_info=True)
    finally:
        # 取消所有未完成的任务
        print("Cancelling remaining tasks...")
        for task in tasks:
            if not task.done():
                task.cancel()
        
        # 等待任务取消完成
        if tasks:
            try:
                await asyncio.wait(tasks, timeout=5)
                print("All tasks cancelled")
                logging.info("所有任务已成功取消")
            except asyncio.CancelledError:
                print("Task cancellation was interrupted")
                logging.info("任务取消过程被中断")
            except Exception as e:
                print(f"Error while cancelling tasks: {str(e)}")
                logging.error(f"取消任务时出错: {e}")
        
        # 确保更新节点状态为已停止
        if redis_client:
            try:
                print("Updating node status to 'stopped'...")
                await update_node_status(redis_client, status="stopped")
                await redis_client.aclose()
                print("Redis connection closed")
                logging.info("Redis连接已关闭")
            except Exception as e:
                print(f"Error closing Redis connection: {str(e)}")
                logging.error(f"关闭Redis连接时出错: {e}")

# 程序入口
if __name__ == "__main__":
    print(f"Starting email_verify.py at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Program forcefully interrupted")
        logging.info("程序被强制中断")
    except Exception as e:
        print(f"Program crashed: {str(e)}")
        logging.critical(f"程序崩溃: {e}", exc_info=True)
    print("Program exited") 