#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis邮箱管理工具 - 加载、导出和清除Redis中的邮箱数据
"""

import os
import logging
import redis
import time
import concurrent.futures
import socket
import json
import re
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# Redis配置
REDIS_HOST = os.getenv("REDIS_HOST", "localhost") 
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

# Redis键名
EMAIL_QUEUE_KEY = "emails:queue"  # 待处理邮箱队列
SUCCESS_KEY = "emails:success"    # 成功邮箱集合
ERROR_KEY = "emails:error"        # 失败邮箱集合
PROCESSING_KEY = "emails:processing"  # 处理中邮箱集合
STATS_KEY = "emails:stats"  # 统计数据

# 节点状态相关键名（与monitor_servers.py保持一致）
NODE_PREFIX = "emails:node:"  # 节点状态信息前缀

# 配置日志
LOGS_FOLDER = "logs"
os.makedirs(LOGS_FOLDER, exist_ok=True)

# 配置日志
log_file = os.path.join(LOGS_FOLDER, "redis_email_manager.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file, mode='a', encoding='utf-8'),  # 日志文件输出
        logging.StreamHandler()  # 控制台输出
    ]
)
logger = logging.getLogger(__name__)

# 默认输出文件
EMAILS_FILE = "redis_emails.txt"  # 待处理和处理中的邮箱
SUCCESS_FILE = "redis_success.txt"  # 成功的邮箱
ERROR_FILE = "redis_error.txt"  # 失败的邮箱
OFFLINE_NODES_FILE = "offline_nodes.txt"  # 离线节点

def is_valid_email(email):
    """验证邮箱格式是否有效"""
    # 基本的邮箱格式验证正则表达式
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def clean_and_validate_email(line):
    """清理和验证邮箱字符串
    
    Args:
        line: 原始行内容
        
    Returns:
        str: 有效的邮箱地址，如果无效则返回None
    """
    # 移除首尾空白字符
    line = line.strip()
    if not line:
        return None
    
    # 尝试提取邮箱地址（处理类似 "mhhmth   mhh5510 <EMAIL>" 的情况）
    # 查找包含@符号的部分
    email_match = re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', line)
    if email_match:
        email = email_match.group()
        # 验证提取的邮箱是否有效
        if is_valid_email(email):
            return email
    
    return None

def connect_redis():
    """连接到Redis服务器"""
    try:
        client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True  # 自动解码响应
        )
        client.ping()  # 测试连接
        logger.info(f"成功连接到Redis: {REDIS_HOST}:{REDIS_PORT}")
        return client
    except Exception as e:
        logger.error(f"连接Redis失败: {e}")
        return None

# ============ 邮箱加载功能 ============

def load_emails_to_redis(file_path="emails.txt", batch_size=10000):
    """将邮箱文件加载到Redis队列 - 优化版本"""
    client = connect_redis()
    if not client:
        return 0
        
    if not os.path.exists(file_path):
        logger.error(f"错误: 文件 {file_path} 不存在")
        return 0
    
    # 读取所有邮箱，添加错误处理、UTF-8编码和邮箱验证
    emails = []
    skipped_lines = 0
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                email = clean_and_validate_email(line)
                if email:
                    emails.append(email)
                elif line.strip():  # 如果行不为空但邮箱无效
                    skipped_lines += 1
                    if skipped_lines <= 10:  # 只显示前10个无效行的详情
                        logger.warning(f"第{line_num}行包含无效邮箱格式: {line.strip()[:50]}...")
        
        if skipped_lines > 0:
            logger.info(f"跳过了 {skipped_lines} 行无效邮箱格式")
    except Exception as e:
        logger.error(f"读取文件时出错: {e}")
        return 0
    
    if not emails:
        return 0

    # 批量处理以提高效率
    start_time = time.time()
    total_added = 0
    total_emails = len(emails)
    
    # 按批次处理
    for i in range(0, total_emails, batch_size):
        batch = emails[i:i+batch_size]
        
        # 获取已存在的邮箱
        pipe = client.pipeline()
        for email in batch:
            pipe.sismember(SUCCESS_KEY, email)
            pipe.sismember(ERROR_KEY, email)
            pipe.sismember(PROCESSING_KEY, email)
        results = pipe.execute()
        
        # 找出需要添加的邮箱
        to_add = []
        for idx, email in enumerate(batch):
            # 每个邮箱有3个检查结果
            base_idx = idx * 3
            if not (results[base_idx] or results[base_idx+1] or results[base_idx+2]):
                to_add.append(email)
        
        # 批量添加到队列
        if to_add:
            client.rpush(EMAIL_QUEUE_KEY, *to_add)
            total_added += len(to_add)
        
        # 日志进度
        logger.info(f"已处理 {i+len(batch)}/{total_emails} 个邮箱, 添加了 {len(to_add)} 个")
    
    duration = time.time() - start_time
    logger.info(f"总计添加了 {total_added} 个邮箱, 耗时 {duration:.2f} 秒")
    client.close()
    return total_added

def load_emails_concurrent(file_path="emails.txt", max_workers=10, chunk_size=5000):
    """并发加载邮箱到Redis队列"""
    if not os.path.exists(file_path):
        logger.error(f"错误: 文件 {file_path} 不存在")
        return 0
    
    # 读取所有邮箱，添加错误处理、UTF-8编码和邮箱验证
    all_emails = []
    skipped_lines = 0
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                email = clean_and_validate_email(line)
                if email:
                    all_emails.append(email)
                elif line.strip():  # 如果行不为空但邮箱无效
                    skipped_lines += 1
                    if skipped_lines <= 10:  # 只显示前10个无效行的详情
                        logger.warning(f"第{line_num}行包含无效邮箱格式: {line.strip()[:50]}...")
        
        if skipped_lines > 0:
            logger.info(f"跳过了 {skipped_lines} 行无效邮箱格式")
    except Exception as e:
        logger.error(f"读取文件时出错: {e}")
        return 0
    
    if not all_emails:
        return 0
    
    total_emails = len(all_emails)
    logger.info(f"开始并发处理 {total_emails} 个邮箱, 使用 {max_workers} 个工作线程")
    
    # 分割邮箱列表为多个块
    chunks = []
    for i in range(0, total_emails, chunk_size):
        chunks.append(all_emails[i:i+chunk_size])
    
    # 处理单个块的函数
    def process_chunk(emails_chunk):
        # 为每个线程创建独立的Redis连接
        local_redis = redis.Redis(
            host=REDIS_HOST, 
            port=REDIS_PORT, 
            db=REDIS_DB, 
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        
        # 获取已存在的邮箱
        pipe = local_redis.pipeline()
        for email in emails_chunk:
            pipe.sismember(SUCCESS_KEY, email)
            pipe.sismember(ERROR_KEY, email)
            pipe.sismember(PROCESSING_KEY, email)
        results = pipe.execute()
        
        # 找出需要添加的邮箱
        to_add = []
        for idx, email in enumerate(emails_chunk):
            base_idx = idx * 3
            if not (results[base_idx] or results[base_idx+1] or results[base_idx+2]):
                to_add.append(email)
        
        # 批量添加到队列
        if to_add:
            local_redis.rpush(EMAIL_QUEUE_KEY, *to_add)
        
        # 关闭连接
        local_redis.close()
        return len(to_add)
    
    # 使用线程池并发处理
    start_time = time.time()
    total_added = 0
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_chunk = {executor.submit(process_chunk, chunk): i for i, chunk in enumerate(chunks)}
        
        for future in concurrent.futures.as_completed(future_to_chunk):
            chunk_idx = future_to_chunk[future]
            try:
                added = future.result()
                total_added += added
                logger.info(f"块 {chunk_idx+1}/{len(chunks)} 完成, 添加了 {added} 个邮箱")
            except Exception as e:
                logger.error(f"处理块 {chunk_idx+1}/{len(chunks)} 时出错: {e}")
    
    duration = time.time() - start_time
    logger.info(f"并发处理完成! 总计添加了 {total_added} 个邮箱, 耗时 {duration:.2f} 秒")
    return total_added

# ============ 邮箱导出功能 ============

def export_queue_to_file(client, key, file_path, batch_size=5000):
    """
    使用 LRANGE 分批导出 Redis 列表数据到文件
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            start = 0
            total = 0
            while True:
                items = client.lrange(key, start, start + batch_size - 1)
                if not items:
                    break
                for item in items:
                    f.write(f"{item}\n")
                total += len(items)
                start += batch_size
                logger.info(f"已写入 {total} 条记录到 {file_path}")
            logger.info(f"成功导出 {total} 条记录到 {file_path}")
            return total
    except Exception as e:
        logger.error(f"导出 {key} 到 {file_path} 失败: {e}")
        return 0

def export_set_to_file(client, key, file_path, batch_size=5000):
    """
    使用 SSCAN 分批导出 Redis 集合数据到文件，防止内存溢出和超时
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            cursor = 0
            total = 0
            while True:
                cursor, items = client.sscan(key, cursor=cursor, count=batch_size)
                for item in items:
                    f.write(f"{item}\n")
                total += len(items)
                logger.info(f"已写入 {total} 条记录到 {file_path}")
                if cursor == 0:  # 游标回到 0 表示遍历完成
                    break
            logger.info(f"成功导出 {total} 条记录到 {file_path}")
            return total
    except Exception as e:
        logger.error(f"导出 {key} 到 {file_path} 失败: {e}")
        return 0

def export_multiple_sets_to_file(client, keys, file_path, batch_size=5000):
    """
    合并多个 Redis 集合并导出到一个文件（流式写入）
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            total = 0
            seen = set()  # 去重邮箱
            for key in keys:
                cursor = 0
                while True:
                    cursor, items = client.sscan(key, cursor=cursor, count=batch_size)
                    for item in items:
                        if item not in seen:
                            seen.add(item)
                            f.write(f"{item}\n")
                            total += 1
                    if cursor == 0:
                        break
                logger.info(f"已处理集合 {key}")
            logger.info(f"成功导出 {total} 条唯一记录到 {file_path}")
            return total
    except Exception as e:
        logger.error(f"导出多个集合到 {file_path} 失败: {e}")
        return 0

def export_all_data(emails_file=EMAILS_FILE, success_file=SUCCESS_FILE, error_file=ERROR_FILE):
    """导出所有Redis数据到文件"""
    client = connect_redis()
    if not client:
        return
    
    try:
        # 导出待处理和处理中的邮箱
        pending_count = export_multiple_sets_to_file(
            client, 
            [PROCESSING_KEY], 
            emails_file
        )
        
        # 导出成功的邮箱
        success_count = export_set_to_file(
            client,
            SUCCESS_KEY,
            success_file
        )
        
        # 导出失败的邮箱
        error_count = export_set_to_file(
            client,
            ERROR_KEY,
            error_file
        )
        
        # 输出统计信息
        logger.info("==== 导出完成 ====")
        logger.info(f"待处理和处理中: {pending_count} 个邮箱 -> {emails_file}")
        logger.info(f"验证成功: {success_count} 个邮箱 -> {success_file}")
        logger.info(f"验证失败: {error_count} 个邮箱 -> {error_file}")
        logger.info(f"总计: {pending_count + success_count + error_count} 个邮箱")
        
        # 从stats中获取处理统计
        stats = client.hgetall(STATS_KEY)
        if stats:
            logger.info("==== 处理统计 ====")
            for key, value in stats.items():
                logger.info(f"{key}: {value}")
    except Exception as e:
        logger.error(f"导出数据时发生错误: {e}")
    finally:
        client.close()

# ============ 邮箱清除功能 ============

def clear_redis_data(clear_success=False, clear_error=False, clear_queue=False, clear_processing=False, clear_all=False, confirm=False):
    """清除Redis中的邮箱数据"""
    client = connect_redis()
    if not client:
        return False
    
    if clear_all:
        clear_success = clear_error = clear_queue = clear_processing = True
    
    if not any([clear_success, clear_error, clear_queue, clear_processing]):
        logger.error("没有指定要清除的数据类型")
        client.close()
        return False
    
    # 确认操作
    if not confirm:
        logger.warning("清除操作需要确认，请添加 --confirm 参数")
        client.close()
        return False
        
    try:
        total_cleared = 0
        
        # 清除成功邮箱
        if clear_success:
            count = client.scard(SUCCESS_KEY)
            client.delete(SUCCESS_KEY)
            logger.info(f"已清除 {count} 个成功邮箱")
            total_cleared += count
            
        # 清除失败邮箱
        if clear_error:
            count = client.scard(ERROR_KEY)
            client.delete(ERROR_KEY)
            logger.info(f"已清除 {count} 个失败邮箱")
            total_cleared += count
            
        # 清除待处理队列
        if clear_queue:
            count = client.llen(EMAIL_QUEUE_KEY)
            client.delete(EMAIL_QUEUE_KEY)
            logger.info(f"已清除 {count} 个待处理邮箱")
            total_cleared += count
            
        # 清除处理中邮箱
        if clear_processing:
            count = client.scard(PROCESSING_KEY)
            client.delete(PROCESSING_KEY)
            logger.info(f"已清除 {count} 个处理中邮箱")
            total_cleared += count
            
        # 清除统计数据
        if clear_all:
            client.delete(STATS_KEY)
            logger.info("已清除统计数据")
            
        logger.info(f"清除操作完成，总计清除 {total_cleared} 个邮箱")
        client.close()
        return True
    except Exception as e:
        logger.error(f"清除数据时发生错误: {e}")
        client.close()
        return False

# ============ 节点状态功能 ============

def check_node_active(node_data, timeout=180):
    """检查节点是否活跃（参考monitor_servers.py的逻辑）
    
    Args:
        node_data: 节点数据
        timeout: 心跳超时时间(秒)，默认180秒(3分钟)
        
    Returns:
        bool: 如果节点活跃返回True，否则返回False
    """
    if not node_data or 'heartbeat' not in node_data:
        return False
    
    try:
        heartbeat = float(node_data['heartbeat'])
        now = time.time()
        # 如果心跳在timeout秒内，认为是活跃的
        return (now - heartbeat) < timeout
    except (ValueError, TypeError):
        return False

def load_ssh_config(ssh_config_file='ssh.json'):
    """加载SSH配置文件"""
    try:
        with open(ssh_config_file, 'r', encoding='utf-8', errors='ignore') as f:
            config = json.load(f)
        logger.info(f"成功加载 {len(config)} 台服务器配置")
        return config
    except Exception as e:
        logger.error(f"加载SSH配置文件失败: {e}")
        return []

def get_offline_nodes(ssh_config_file='ssh.json', timeout=180):
    """获取离线节点IP列表"""
    client = connect_redis()
    if not client:
        return []
    
    try:
        # 加载SSH配置获取所有服务器
        servers = load_ssh_config(ssh_config_file)
        if not servers:
            logger.error(f"无法加载SSH配置文件: {ssh_config_file}")
            return []
        
        offline_nodes = []
        
        # 检查每个节点状态
        for server in servers:
            node_id = server['host']
            node_key = f"{NODE_PREFIX}{node_id}"
            
            # 获取节点状态
            node_data = client.hgetall(node_key)
            
            # 使用check_node_active函数检查是否离线
            if not check_node_active(node_data, timeout):
                logger.info(f"检测到离线节点: {node_id}")
                offline_nodes.append(node_id)
                
        logger.info(f"共检测到 {len(offline_nodes)}/{len(servers)} 个离线节点")
        return offline_nodes
    except Exception as e:
        logger.error(f"获取离线节点失败: {e}")
        return []
    finally:
        client.close()

def export_offline_nodes(file_path=OFFLINE_NODES_FILE, ssh_config_file='ssh.json', timeout=180):
    """导出离线节点IP到文件"""
    try:
        # 获取离线节点列表
        offline_nodes = get_offline_nodes(ssh_config_file, timeout)
        
        if not offline_nodes:
            logger.info("没有检测到离线节点")
            return 0
            
        # 写入文件，添加UTF-8编码
        with open(file_path, 'w', encoding='utf-8') as f:
            for node in offline_nodes:
                f.write(f"{node}\n")
        
        logger.info(f"已将 {len(offline_nodes)} 个离线节点IP导出到 {file_path}")
        return len(offline_nodes)
    except Exception as e:
        logger.error(f"导出离线节点IP失败: {e}")
        return 0

# ============ 通用功能 ============

def redis_queue_info():
    """获取Redis队列状态信息"""
    client = connect_redis()
    if not client:
        return {}
        
    queue_length = client.llen(EMAIL_QUEUE_KEY)
    success_count = client.scard(SUCCESS_KEY)
    error_count = client.scard(ERROR_KEY)
    processing_count = client.scard(PROCESSING_KEY)
    
    print(f"======= Redis队列状态 =======")
    print(f"待处理邮箱: {queue_length}")
    print(f"成功邮箱: {success_count}")
    print(f"失败邮箱: {error_count}")
    print(f"处理中邮箱: {processing_count}")
    print(f"总计: {queue_length + success_count + error_count + processing_count}")
    print(f"============================")
    
    # 从stats中获取处理统计
    stats = client.hgetall(STATS_KEY)
    if stats:
        print(f"======= 处理统计 =======")
        for key, value in stats.items():
            print(f"{key}: {value}")
        print(f"========================")
            
    client.close()
    return {
        "queue": queue_length,
        "success": success_count,
        "error": error_count,
        "processing": processing_count
    }

# ============ Redis清空功能 ============

def flush_redis_db(confirm=False):
    """清空Redis数据库中的所有数据"""
    client = connect_redis()
    if not client:
        return False
    
    # 确认操作
    if not confirm:
        logger.warning("清空Redis数据库是危险操作，请添加 --confirm 参数确认")
        client.close()
        return False
    
    try:
        # 执行FLUSHDB命令
        client.flushdb()
        logger.info("已清空Redis数据库中的所有数据")
        client.close()
        return True
    except Exception as e:
        logger.error(f"清空Redis数据库失败: {e}")
        client.close()
        return False

# ============ 主函数 ============

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Redis邮箱管理工具")
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 加载邮箱命令
    load_parser = subparsers.add_parser("load", help="加载邮箱到Redis")
    load_parser.add_argument("-f", "--file", default="emails.txt", help="邮箱文件路径，默认为emails.txt")
    load_parser.add_argument("-c", "--concurrent", action="store_true", help="使用并发模式加载邮箱")
    load_parser.add_argument("-w", "--workers", type=int, default=10, help="并发工作线程数，默认为10")
    load_parser.add_argument("-b", "--batch", type=int, default=10000, help="批处理大小，默认为10000")
    
    # 导出邮箱命令
    export_parser = subparsers.add_parser("export", help="导出Redis中的邮箱")
    export_parser.add_argument("--emails", default=EMAILS_FILE, help=f"待处理和处理中的邮箱输出文件，默认为{EMAILS_FILE}")
    export_parser.add_argument("--success", default=SUCCESS_FILE, help=f"成功邮箱输出文件，默认为{SUCCESS_FILE}")
    export_parser.add_argument("--error", default=ERROR_FILE, help=f"失败邮箱输出文件，默认为{ERROR_FILE}")
    
    # 清除邮箱命令
    clear_parser = subparsers.add_parser("clear", help="清除Redis中的邮箱")
    clear_parser.add_argument("--success", action="store_true", help="清除成功邮箱")
    clear_parser.add_argument("--error", action="store_true", help="清除失败邮箱")
    clear_parser.add_argument("--queue", action="store_true", help="清除待处理队列")
    clear_parser.add_argument("--processing", action="store_true", help="清除处理中邮箱")
    clear_parser.add_argument("--all", action="store_true", help="清除所有邮箱和统计数据")
    clear_parser.add_argument("--confirm", action="store_true", help="确认清除操作")
    
    # 离线节点命令
    offline_parser = subparsers.add_parser("offline", help="导出离线节点IP")
    offline_parser.add_argument("-o", "--output", default=OFFLINE_NODES_FILE, help=f"输出文件路径，默认为{OFFLINE_NODES_FILE}")
    offline_parser.add_argument("-s", "--ssh", default="ssh.json", help="SSH配置文件路径，默认为ssh.json")
    offline_parser.add_argument("-t", "--timeout", type=int, default=180, help="心跳超时时间(秒)，默认180秒")
    
    # 清空Redis数据库命令
    flush_parser = subparsers.add_parser("flush", help="清空Redis数据库中的所有数据")
    flush_parser.add_argument("--confirm", action="store_true", help="确认清空操作")
    
    # 查看状态命令
    info_parser = subparsers.add_parser("info", help="查看Redis队列状态")
    
    args = parser.parse_args()
    
    # 处理命令
    if args.command == "load":
        # 加载邮箱到Redis
        start_time = time.time()
        if args.concurrent:
            loaded = load_emails_concurrent(args.file, args.workers, args.batch)
        else:
            loaded = load_emails_to_redis(args.file, args.batch)
        duration = time.time() - start_time
        
        logger.info(f"操作完成！共加载 {loaded} 个邮箱到队列，总耗时: {duration:.2f}秒")
        redis_queue_info()
        
    elif args.command == "export":
        # 导出Redis数据
        logger.info("开始导出Redis数据...")
        export_all_data(args.emails, args.success, args.error)
        logger.info("导出完成")
        
    elif args.command == "clear":
        # 清除Redis数据
        logger.info("开始清除Redis数据...")
        if clear_redis_data(
            args.success, args.error, args.queue, args.processing, args.all, args.confirm
        ):
            logger.info("清除操作成功完成")
        else:
            logger.error("清除操作失败")
    
    elif args.command == "offline":
        # 导出离线节点IP
        logger.info("开始获取离线节点...")
        exported = export_offline_nodes(args.output, args.ssh, args.timeout)
        if exported > 0:
            logger.info(f"已导出 {exported} 个离线节点IP到 {args.output}")
        else:
            logger.info("没有检测到离线节点")
    
    elif args.command == "flush":
        # 清空Redis数据库
        logger.info("开始清空Redis数据库...")
        if flush_redis_db(args.confirm):
            logger.info("Redis数据库已清空")
        else:
            logger.error("清空Redis数据库失败")
            
    elif args.command == "info":
        # 显示Redis队列信息
        redis_queue_info()
        
    else:
        parser.print_help()
