# 安装说明

本项目提供了两个安装脚本，适用于新安装的Ubuntu系统。

## 脚本说明

### 1. install.sh - 完整安装脚本（推荐）

**特性：**
- ✅ 完整的错误处理和日志输出
- ✅ 网络连接检查
- ✅ 防止apt-get卡死的机制
- ✅ 自动检测和安装Python/pip
- ✅ 支持虚拟环境创建
- ✅ 依赖安装重试机制
- ✅ 系统信息检查
- ✅ 自动创建启动脚本

**使用方法：**
```bash
chmod +x install.sh
./install.sh
```

### 2. quick_install.sh - 快速安装脚本

**特性：**
- ⚡ 快速简洁
- ✅ 基本的错误处理
- ✅ 防止卡死机制
- ✅ 自动安装所有必需组件

**使用方法：**
```bash
chmod +x quick_install.sh
./quick_install.sh
```

## 安装内容

两个脚本都会自动安装：

1. **系统更新**
   - 更新apt包列表
   - 配置非交互模式防止卡死

2. **基础工具**
   - curl, wget
   - build-essential
   - git
   - 其他必需工具

3. **Python环境**
   - Python 3.x
   - pip3
   - python3-venv
   - python3-dev

4. **项目依赖**
   - redis==4.5.5
   - aiohttp==3.8.4
   - python-dotenv==1.0.0
   - requests==2.28.2
   - psutil==5.9.5
   - asyncssh==2.14.0

## 防卡死机制

脚本包含以下防卡死措施：

1. **非交互模式**
   ```bash
   export DEBIAN_FRONTEND=noninteractive
   ```

2. **dpkg配置**
   - 自动使用默认配置
   - 保留旧配置文件

3. **超时处理**
   - apt-get操作设置超时
   - 失败时自动清理缓存重试

4. **网络检查**
   - 安装前检查网络连接
   - 确保能够下载包

## 使用建议

1. **推荐使用完整版install.sh**
   - 更好的错误处理
   - 详细的日志输出
   - 支持虚拟环境

2. **网络环境**
   - 确保网络连接稳定
   - 如果在国内，建议配置apt镜像源

3. **权限要求**
   - 需要sudo权限安装系统包
   - 建议使用普通用户运行（脚本会提示）

4. **系统要求**
   - Ubuntu 18.04 或更高版本
   - 至少1GB可用磁盘空间
   - 稳定的网络连接

## 故障排除

### 如果安装失败：

1. **检查网络连接**
   ```bash
   ping -c 4 *******
   ```

2. **清理apt缓存**
   ```bash
   sudo apt-get clean
   sudo rm -rf /var/lib/apt/lists/*
   sudo apt-get update
   ```

3. **手动安装Python**
   ```bash
   sudo apt-get install python3 python3-pip
   ```

4. **检查磁盘空间**
   ```bash
   df -h
   ```

### 常见问题：

**Q: apt-get update卡死怎么办？**
A: 脚本已包含超时和重试机制，会自动处理

**Q: pip安装依赖失败？**
A: 脚本包含重试机制，会尝试3次安装

**Q: 需要特定Python版本？**
A: 脚本会检查Python版本，建议3.7+

## 安装后

安装完成后，可以使用以下方式启动程序：

1. **使用生成的启动脚本**
   ```bash
   ./start.sh  # 完整版生成
   ./run.sh    # 快速版生成
   ```

2. **直接运行**
   ```bash
   python3 email_verify.py
   ```

3. **使用虚拟环境（如果创建了）**
   ```bash
   source venv/bin/activate
   python3 email_verify.py
   ```
