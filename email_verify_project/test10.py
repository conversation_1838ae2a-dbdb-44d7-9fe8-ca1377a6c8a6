#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test10.py - 异步测试 email_verify.py 的核心功能

测试步骤：
1. 获取token和PoW参数（异步方式）
2. 执行PoW验证
3. 验证邮箱状态（异步方式）

基于 email_verify.py 的异步逻辑实现
"""

import json
import time
import uuid
import powlib
import asyncio
from pow_utils import verify_pow_result

# 导入request_builder模块
import request_builder

# 邮箱验证相关的URL常量
REGISTRATION_URL = "https://login.account.rakuten.com/v2/registration"
REGISTRATION_CHECK_URL = "https://login.account.rakuten.com/v2/registration/1?dry_run=true"

async def get_token_and_pow_params_async(timeout=5, proxy=None):
    """
    获取token和PoW参数（异步版本）
    基于 email_verify.py 中的 get_token() 函数
    """
    try:
        print("正在获取token和PoW参数...")
        
        # 生成tracking_id
        tracking_id = request_builder.generate_tracking_id()
        print(f"生成tracking_id: {tracking_id}")
        
        # 获取gc_url
        gc_url = request_builder.get_gc_url(tracking_id)
        print(f"请求URL: {gc_url}")
        
        # 构造payload
        payload = request_builder.construct_payload()
        
        # 异步发送请求获取token和挑战参数
        response = await request_builder.send_post_request_async(gc_url, payload, timeout=timeout, proxy=proxy)
        
        if response.status_code != 200:
            print(f"获取token失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
        
        # 解析响应
        try:
            result = json.loads(response.text)
            token = result.get("token")
            if not token:
                print("响应中没有token字段")
                return None
            
            # 解析挑战参数
            mdata = json.loads(result.get("mdata"))
            cdata = json.loads(result.get("cdata"))
            
            # 获取PoW参数
            mask = mdata.get("body").get("mask")
            key = mdata.get("body").get("key")
            seed = mdata.get("body").get("seed")
            
            # 创建token对象
            token_obj = {
                "token": token,
                "mask": mask,
                "key": key,
                "seed": seed,
                "tracking_id": tracking_id,
                "created_time": time.time()
            }
            
            # 检查mask长度（基于email_verify.py的逻辑）
            if len(mask) > 5:
                print(f"⚠️ 检测到mask大于5: {mask}，这可能导致IP被禁用")
                return {"special": True, "mask": mask}

            print(f"✅ 成功获取token和PoW参数:")
            print(f"   Token: {token[:10]}...")
            print(f"   Seed: {seed}")
            print(f"   Mask: {mask}")
            print(f"   Key: {key}")
            
            return token_obj
            
        except Exception as e:
            print(f"解析token响应失败: {e}")
            return None
            
    except Exception as e:
        print(f"获取token过程中出错: {e}")
        return None

def solve_and_verify_pow(token_obj):
    """
    求解并验证PoW
    注意: PoW计算本身是CPU密集型，不适合异步化，保留同步实现
    """
    try:
        print("\n正在执行PoW求解...")
        
        # 检查是否是特殊标记
        if isinstance(token_obj, dict) and token_obj.get("special"):
            print(f"❌ 检测到特殊mask: {token_obj.get('mask')}，跳过PoW求解")
            return None
        
        seed = token_obj.get("seed")
        mask = token_obj.get("mask")
        key = token_obj.get("key")
        
        print(f"PoW参数: seed={seed}, mask={mask}, key={key}")
        
        # 使用powlib进行PoW求解 (同步计算)
        start_time = time.time()
        pow_result = powlib.solve_pow(seed=seed, mask=mask, prefix=key, threads=8, max_iterations=0)
        solve_time = time.time() - start_time
        
        if not pow_result or not pow_result.result:
            print("❌ PoW求解失败")
            return None
        
        print(f"✅ PoW求解成功:")
        print(f"   结果: {pow_result.result}")
        print(f"   迭代次数: {pow_result.iterations}")
        print(f"   耗时: {solve_time:.2f} 秒")
        
        # 验证PoW结果
        print("\n正在验证PoW结果...")
        is_valid, message = verify_pow_result(
            pow_result.result,
            key,
            seed,
            mask
        )
        
        if is_valid:
            print(f"✅ PoW验证通过: {message}")
            
            # 将结果添加到token对象中
            token_obj["cres"] = pow_result.result
            token_obj["pow_iterations"] = pow_result.iterations
            token_obj["pow_time"] = solve_time
            
            return token_obj
        else:
            print(f"❌ PoW验证失败: {message}")
            return None
            
    except Exception as e:
        print(f"PoW求解过程中出错: {e}")
        return None

async def verify_email_status_async(email, token_obj, timeout=5, proxy=None):
    """
    异步验证邮箱状态
    基于 email_verify.py 中的异步验证逻辑
    
    返回值：
    - True: 邮箱已注册（确定）
    - False: 邮箱未注册（确定）
    - None: 结果不确定
    """
    try:
        print(f"\n正在验证邮箱: {email}")
        
        token = token_obj.get("token")
        cres = token_obj.get("cres")
        
        if not token or not cres:
            print("❌ 缺少必要的token或cres参数")
            return None
        
        # 第一步：获取注册token（异步）
        print("正在获取注册token...")
        reg_token = await request_builder.get_registration_token_async(timeout=timeout, proxy=proxy)
        
        if not reg_token:
            print(f"❌ 无法获取注册token，邮箱验证中止: {email}")
            return None
        
        print(f"✅ 成功获取注册token: {reg_token[:10]}...")
        
        # 第二步：发送邮箱注册验证请求（异步）
        print(f"发送邮箱注册验证请求...")
        response = await request_builder.verify_email_registration_async(
            email=email,
            cres=cres,
            token=token,
            reg_token=reg_token,
            timeout=timeout,
            proxy=proxy
        )
        
        if not response:
            print(f"❌ 请求发送失败: {email}")
            return None
        
        print(f"收到响应: 状态码={response.status_code}")
        
        # 第三步：解析响应判断结果（基于email_verify.py的逻辑）
        if response.status_code == 200:
            # 状态码200表示邮箱未注册，验证失败
            try:
                error_data = json.loads(response.text)
                print(f"❌ 邮箱验证失败 (邮箱未注册): {email}")
                print(f"响应内容: {error_data}")
                return False  # 确定：邮箱未注册
            except json.JSONDecodeError:
                print(f"❓ 解析200响应JSON失败: {email}")
                return None
        elif response.status_code == 400:
            # 解析400错误内容
            try:
                error_data = json.loads(response.text)
                if (error_data.get("errorCode") == "VALIDATION_ERROR" and 
                        any(err.get("reasonCode") == "ALREADY_EXISTS" and err.get("field") == "email" 
                            for err in error_data.get("errors", []))):
                    # 发现ALREADY_EXISTS错误，表示邮箱已注册
                    print(f"✅ 邮箱验证成功 (邮箱已注册): {email}")
                    print(f"响应内容: {error_data}")
                    return True  # 确定：邮箱已注册
                else:
                    # 其他400错误
                    print(f"❓ 邮箱验证结果不确定 (未知400错误): {email}")
                    print(f"错误内容: {error_data}")
                    return None  # 不确定
            except json.JSONDecodeError:
                print(f"❓ 解析400响应JSON失败: {email}")
                return None
        else:
            # 其他状态码
            print(f"❓ 邮箱验证结果不确定: {email}, 状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return None  # 不确定
            
    except Exception as e:
        print(f"❌ 邮箱验证过程中出错: {email}, 错误: {e}")
        return None  # 不确定

async def test_email_verification_flow_async():
    """异步测试完整的邮箱验证流程"""
    print("=" * 60)
    print("开始异步测试 email_verify.py 的核心功能")
    print("=" * 60)
    
    # 第一步：获取token和PoW参数（异步）
    print("\n【步骤1】获取token和PoW参数")
    print("-" * 40)
    
    token_obj = await get_token_and_pow_params_async()
    if not token_obj:
        print("❌ 获取token失败，测试终止")
        return
    
    # 检查是否是特殊情况
    if isinstance(token_obj, dict) and token_obj.get("special"):
        print(f"⚠️ 检测到特殊mask: {token_obj.get('mask')}，建议停止测试")
        return
    
    # 第二步：执行PoW验证（同步）
    print("\n【步骤2】执行PoW求解和验证")
    print("-" * 40)
    
    verified_token_obj = solve_and_verify_pow(token_obj)
    if not verified_token_obj:
        print("❌ PoW验证失败，测试终止")
        return
    
    # 第三步：验证邮箱状态（异步）
    print("\n【步骤3】验证邮箱状态")
    print("-" * 40)
    
    # 测试邮箱列表
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    # 统计验证结果
    stats = {"registered": 0, "not_registered": 0, "uncertain": 0}
    
    for i, email in enumerate(test_emails, 1):
        print(f"\n验证邮箱 {i}/{len(test_emails)}: {email}")
        print("-" * 30)
        
        result = await verify_email_status_async(email, verified_token_obj)
        
        if result is True:
            print(f"✅ 邮箱 {email} 已注册（确定）")
            stats["registered"] += 1
        elif result is False:
            print(f"❌ 邮箱 {email} 未注册（确定）")
            stats["not_registered"] += 1
        else:
            print(f"❓ 邮箱 {email} 验证结果不确定")
            stats["uncertain"] += 1
        
        # 在验证邮箱之间稍作延迟，避免请求过于频繁
        if i < len(test_emails):
            print("等待1秒后继续...")
            await asyncio.sleep(1)
    
    # 显示统计结果
    print("\n" + "=" * 60)
    print("邮箱验证统计结果")
    print("=" * 60)
    print(f"总验证数量: {len(test_emails)}")
    print(f"已注册邮箱: {stats['registered']}")
    print(f"未注册邮箱: {stats['not_registered']}")
    print(f"不确定结果: {stats['uncertain']}")
    
    if len(test_emails) > 0:
        success_rate = (stats['registered'] + stats['not_registered']) / len(test_emails) * 100
        print(f"确定结果率: {success_rate:.1f}%")
    
    print("\n测试完成！")

async def test_single_email_async(email, token_str=None, cres_str=None):
    """异步测试单个邮箱（可选择使用已有的token和cres）"""
    print(f"测试单个邮箱: {email}")
    
    if token_str and cres_str:
        # 使用提供的token和cres
        print("使用提供的token和cres参数")
        token_obj = {
            "token": token_str,
            "cres": cres_str
        }
        print(f"Token: {token_str[:10] if token_str else 'None'}...")
        print(f"Cres: {cres_str if cres_str else 'None'}")
    else:
        # 完整流程获取token和执行PoW
        print("执行完整流程获取token和PoW...")
        
        # 异步获取token
        token_obj = await get_token_and_pow_params_async()
        if not token_obj:
            print("❌ 获取token失败")
            return
        
        # 检查特殊情况
        if isinstance(token_obj, dict) and token_obj.get("special"):
            print(f"⚠️ 检测到特殊mask: {token_obj.get('mask')}")
            return
        
        # 执行PoW (同步)
        token_obj = solve_and_verify_pow(token_obj)
        if not token_obj:
            print("❌ PoW验证失败")
            return
    
    # 异步验证邮箱
    result = await verify_email_status_async(email, token_obj)
    
    if result is True:
        print(f"✅ 邮箱 {email} 已注册（确定）")
    elif result is False:
        print(f"❌ 邮箱 {email} 未注册（确定）")
    else:
        print(f"❓ 邮箱 {email} 验证结果不确定")
    
    return result

async def test_multiple_emails_async(emails, parallel=False):
    """
    异步测试多个邮箱
    
    参数:
    - emails: 邮箱列表
    - parallel: 是否并行验证
    """
    # 获取一次token和PoW结果
    token_obj = await get_token_and_pow_params_async()
    if not token_obj:
        print("❌ 获取token失败")
        return
    
    # 检查特殊情况
    if isinstance(token_obj, dict) and token_obj.get("special"):
        print(f"⚠️ 检测到特殊mask: {token_obj.get('mask')}")
        return
    
    # 执行PoW
    verified_token_obj = solve_and_verify_pow(token_obj)
    if not verified_token_obj:
        print("❌ PoW验证失败")
        return
    
    # 统计结果
    results = {"registered": 0, "not_registered": 0, "uncertain": 0}
    
    if parallel:
        # 并行验证所有邮箱
        print(f"\n并行验证 {len(emails)} 个邮箱...")
        
        async def verify_email_task(email):
            result = await verify_email_status_async(email, verified_token_obj)
            if result is True:
                results["registered"] += 1
                status = "已注册"
            elif result is False:
                results["not_registered"] += 1
                status = "未注册"
            else:
                results["uncertain"] += 1
                status = "结果不确定"
            print(f"{email}: {status}")
            return result
        
        # 创建所有邮箱的验证任务
        tasks = [verify_email_task(email) for email in emails]
        
        # 并行执行所有任务
        await asyncio.gather(*tasks)
        
    else:
        # 串行验证所有邮箱
        print(f"\n串行验证 {len(emails)} 个邮箱...")
        
        for i, email in enumerate(emails, 1):
            print(f"\n验证邮箱 {i}/{len(emails)}: {email}")
            
            result = await verify_email_status_async(email, verified_token_obj)
            
            if result is True:
                print(f"✅ 邮箱 {email} 已注册")
                results["registered"] += 1
            elif result is False:
                print(f"❌ 邮箱 {email} 未注册")
                results["not_registered"] += 1
            else:
                print(f"❓ 邮箱 {email} 验证结果不确定")
                results["uncertain"] += 1
            
            # 在验证邮箱之间稍作延迟
            if i < len(emails):
                await asyncio.sleep(1)
    
    # 显示统计结果
    print("\n" + "=" * 60)
    print("邮箱验证统计结果")
    print("=" * 60)
    print(f"总验证数量: {len(emails)}")
    print(f"已注册邮箱: {results['registered']}")
    print(f"未注册邮箱: {results['not_registered']}")
    print(f"不确定结果: {results['uncertain']}")
    
    if len(emails) > 0:
        success_rate = (results['registered'] + results['not_registered']) / len(emails) * 100
        print(f"确定结果率: {success_rate:.1f}%")

# 主程序入口
if __name__ == "__main__":
    import sys
    
    async def main():
        """主程序入口点"""
        if len(sys.argv) > 1:
            if sys.argv[1] == "single":
                # 测试单个邮箱
                if len(sys.argv) > 2:
                    email = sys.argv[2]
                    token_str = sys.argv[3] if len(sys.argv) > 3 else None
                    cres_str = sys.argv[4] if len(sys.argv) > 4 else None
                    await test_single_email_async(email, token_str, cres_str)
                else:
                    print("用法: python test10.py single <email> [token] [cres]")
            elif sys.argv[1] == "token":
                # 仅测试获取token
                print("仅测试获取token和PoW参数...")
                token_obj = await get_token_and_pow_params_async()
                if token_obj:
                    print("✅ 成功获取token和PoW参数")
                    if not token_obj.get("special"):
                        print("可以继续进行PoW求解")
                else:
                    print("❌ 获取token失败")
            elif sys.argv[1] == "pow":
                # 测试完整的token获取和PoW流程
                print("测试token获取和PoW流程...")
                token_obj = await get_token_and_pow_params_async()
                if token_obj and not token_obj.get("special"):
                    verified_token_obj = solve_and_verify_pow(token_obj)
                    if verified_token_obj:
                        print("✅ 完整的token和PoW流程测试成功")
                        token = verified_token_obj.get('token')
                        cres = verified_token_obj.get('cres')
                        print(f"Token: {token[:10] if token else 'None'}...")
                        print(f"Cres: {cres if cres else 'None'}")
                    else:
                        print("❌ PoW验证失败")
                else:
                    print("❌ 获取token失败或检测到特殊mask")
            elif sys.argv[1] == "multiple":
                # 测试多个邮箱
                emails = sys.argv[2].split(',') if len(sys.argv) > 2 else ["<EMAIL>", "<EMAIL>"]
                parallel = True if len(sys.argv) > 3 and sys.argv[3].lower() == "true" else False
                await test_multiple_emails_async(emails, parallel)
            else:
                print("用法：")
                print("  python test10.py                      - 运行完整异步邮箱验证流程测试")
                print("  python test10.py token                - 仅测试异步获取token")
                print("  python test10.py pow                  - 测试异步token获取和PoW")
                print("  python test10.py single <email> [token] [cres] - 异步测试单个邮箱")
                print("  python test10.py multiple [emails,comma,separated] [parallel:true/false] - 异步测试多个邮箱")
        else:
            # 默认运行完整异步测试
            await test_email_verification_flow_async()
    
    # 使用asyncio运行主函数
    asyncio.run(main()) 
