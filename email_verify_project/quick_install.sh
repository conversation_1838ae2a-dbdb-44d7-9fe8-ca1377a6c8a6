#!/bin/bash

# 快速安装脚本 - 适用于Ubuntu系统
# 简化版本，专注于核心功能

set -e

echo "🚀 开始快速安装..."

# 设置非交互模式
export DEBIAN_FRONTEND=noninteractive

# 更新系统（防止卡死）
echo "📦 更新系统包..."
sudo apt-get update -y || {
    echo "⚠️  更新失败，清理缓存后重试..."
    sudo apt-get clean
    sudo rm -rf /var/lib/apt/lists/*
    sudo apt-get update -y
}

# 安装Python3和pip
echo "🐍 检查Python和pip..."
sudo apt-get install -y python3 python3-pip python3-venv python3-dev build-essential

# 升级pip
echo "⬆️  升级pip..."
python3 -m pip install --upgrade pip --user

# 安装依赖
echo "📋 安装Python依赖..."
if [[ -f "requirements.txt" ]]; then
    pip3 install -r requirements.txt --user
    echo "✅ 依赖安装完成"
else
    echo "❌ 未找到requirements.txt文件"
    exit 1
fi

# 创建简单启动脚本
cat > run.sh << 'EOF'
#!/bin/bash
echo "启动程序..."
python3 email_verify.py
EOF

chmod +x run.sh

echo "🎉 快速安装完成！"
echo "💡 使用 './run.sh' 启动程序"
