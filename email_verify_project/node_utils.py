import os
import socket
import uuid
import logging
import requests

def get_public_ip():
    """获取公网IP地址，尝试多个API源"""
    apis = [
        'https://ifconfig.me/ip',
        'https://api.ipify.org',
        'https://ipinfo.io/ip',
        'https://icanhazip.com',
        'https://checkip.amazonaws.com',
        'https://ident.me'
    ]
    
    for api in apis:
        try:
            logging.info(f"尝试从 {api} 获取公网IP...")
            response = requests.get(api, timeout=8)
            
            if response.status_code == 200:
                ip = response.text.strip()  # 去除可能的空格和换行符
                if ip and len(ip) > 7:  # 有效IP至少应该是x.x.x.x (7个字符)
                    logging.info(f"成功获取公网IP: '{ip}'")
                    return ip
                else:
                    logging.warning(f"从 {api} 获取的IP无效: '{ip}'")
        except Exception as e:
            logging.warning(f"从 {api} 获取IP失败: {e}")
    
    # 所有API都失败，使用环境变量中的IP_OVERRIDE（如果有）
    ip_override = os.getenv("IP_OVERRIDE")
    if ip_override and ip_override.strip():
        logging.warning(f"使用环境变量指定的IP: {ip_override}")
        return ip_override
    
    # 如果环境变量也没有，则返回机器名和随机ID
    fallback_id = f"default_{socket.gethostname()}"
    logging.error(f"无法获取公网IP，使用备用ID: {fallback_id}")
    return fallback_id

def force_get_public_ip():
    """强制获取公网IP，不使用任何缓存"""
    try:
        # 尝试直接进行DNS查询来获取公网IP (需要互联网连接)
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        logging.info(f"本地IP地址: {local_ip}")
        
        # 尝试所有可用的公网IP获取方法
        if not local_ip:
            public_ip = get_public_ip()
        else:
            public_ip = local_ip
        
        if "default_" in public_ip:
            logging.error("无法获取有效的公网IP!")
            return None
        return public_ip
    except Exception as e:
        logging.error(f"强制获取公网IP失败: {e}")
        return None

def get_node_id():
    """获取节点ID，优先使用环境变量，否则使用主机名和随机ID"""
    # 优先检查环境变量中的NODE_ID
    node_id = os.getenv("NODE_ID")
    if node_id and node_id.strip():
        logging.info(f"使用环境变量中的NODE_ID: {node_id}")
        return node_id
    
    # 生成随机ID
    random_id = f"node_{uuid.uuid4().hex[:8]}_{socket.gethostname()}"
    logging.info(f"生成随机节点ID: {random_id}")
    return random_id

def get_node_ip():
    """获取节点IP地址"""
    logging.info("开始获取公网IP...")
    # 优先检查环境变量中的IP_OVERRIDE
    ip_override = os.getenv("IP_OVERRIDE", "")
    if ip_override and ip_override.strip() != "":
        node_ip = ip_override
        logging.info(f"使用环境变量中的IP_OVERRIDE: {node_ip}")
    else:
        # 环境变量未设置，尝试获取公网IP
        node_ip = force_get_public_ip()
        
        if node_ip is None or "default_" in node_ip:
            # 获取失败，使用机器名
            node_ip = f"no_ip_{socket.gethostname()}"
            logging.warning(f"未能获取有效公网IP，使用替代值: {node_ip}")
        else:
            logging.info(f"成功获取公网IP: {node_ip}")
    
    return node_ip
