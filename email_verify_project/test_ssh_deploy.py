#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试树状SSH部署脚本
"""

import asyncio
import json
import os
import sys
from tree_ssh_deploy import TreeSSHManager, SSHNode

def create_test_ssh_config():
    """创建测试用的SSH配置文件"""
    test_config = [
        {
            "host": "127.0.0.1",  # 本地测试
            "port": 22,
            "username": "test",
            "password": "test"
        }
    ]
    
    with open("test_ssh.json", "w", encoding="utf-8") as f:
        json.dump(test_config, f, indent=2, ensure_ascii=False)
    
    print("创建测试SSH配置文件: test_ssh.json")

def test_tree_structure():
    """测试树状结构构建"""
    print("测试树状结构构建...")
    
    # 创建多个测试服务器
    test_servers = []
    for i in range(12):  # 创建12个服务器测试树状结构
        test_servers.append({
            "host": f"192.168.1.{i+10}",
            "port": 22,
            "username": "test",
            "password": "test"
        })
    
    manager = TreeSSHManager()
    tree_levels = manager.build_tree_structure(test_servers)
    
    print(f"总共 {len(test_servers)} 个服务器")
    print(f"构建了 {len(tree_levels)} 层树状结构")
    
    for level, nodes in enumerate(tree_levels):
        print(f"第{level}层: {len(nodes)} 个节点")
        for node in nodes:
            parent_info = f", 父节点: {node.parent}" if node.parent else ""
            print(f"  - {node.host}{parent_info}")

def test_redis_connection():
    """测试Redis连接"""
    print("测试Redis连接...")
    try:
        from tree_ssh_deploy import redis_client
        redis_client.ping()
        print("✅ Redis连接成功")
        
        # 测试写入和读取
        test_key = "ssh:test"
        test_data = {"test": "data", "timestamp": "123456"}
        redis_client.hset(test_key, mapping=test_data)
        
        result = redis_client.hgetall(test_key)
        print(f"✅ Redis读写测试成功: {result}")
        
        # 清理测试数据
        redis_client.delete(test_key)
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")

def test_file_check():
    """检查需要上传的文件"""
    print("检查需要上传的文件...")
    from tree_ssh_deploy import UPLOAD_FILES
    
    existing_files = []
    missing_files = []
    
    for file_name in UPLOAD_FILES:
        if os.path.exists(file_name):
            existing_files.append(file_name)
            file_size = os.path.getsize(file_name)
            print(f"✅ {file_name} ({file_size} bytes)")
        else:
            missing_files.append(file_name)
            print(f"❌ {file_name} (不存在)")
    
    print(f"\n总结: {len(existing_files)} 个文件存在, {len(missing_files)} 个文件缺失")
    
    if missing_files:
        print("缺失的文件:")
        for file_name in missing_files:
            print(f"  - {file_name}")

async def test_ssh_node():
    """测试SSH节点类"""
    print("测试SSH节点类...")
    
    # 创建一个测试节点（使用无效地址，只测试类的功能）
    node = SSHNode("192.168.1.999", 22, "test", "test")
    
    print(f"节点信息: {node}")
    print(f"初始状态: {node.status}")
    
    # 测试连接（预期会失败）
    result = await node.connect()
    print(f"连接结果: {result}")
    print(f"连接后状态: {node.status}")
    
    # 测试Redis状态更新
    node.update_redis_status("test_status", "test_error")
    print("✅ Redis状态更新测试完成")

def main():
    """主测试函数"""
    print("🧪 树状SSH部署脚本测试")
    print("=" * 50)
    
    # 测试1: 检查文件
    test_file_check()
    print("\n" + "-" * 30 + "\n")
    
    # 测试2: Redis连接
    test_redis_connection()
    print("\n" + "-" * 30 + "\n")
    
    # 测试3: 树状结构
    test_tree_structure()
    print("\n" + "-" * 30 + "\n")
    
    # 测试4: 创建测试配置
    create_test_ssh_config()
    print("\n" + "-" * 30 + "\n")
    
    # 测试5: SSH节点类（异步）
    print("运行异步测试...")
    asyncio.run(test_ssh_node())
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    print("\n📋 使用说明:")
    print("1. 部署项目: python3 tree_ssh_deploy.py deploy")
    print("2. 启动服务: python3 tree_ssh_deploy.py start")
    print("3. 停止服务: python3 tree_ssh_deploy.py stop")
    print("4. 重启服务: python3 tree_ssh_deploy.py restart")
    print("5. 检查状态: python3 tree_ssh_deploy.py status")
    print("6. 自定义命令: python3 tree_ssh_deploy.py command -c 'your_command'")
    print("7. 指定配置文件: python3 tree_ssh_deploy.py deploy -f custom_ssh.json")

if __name__ == "__main__":
    main()
