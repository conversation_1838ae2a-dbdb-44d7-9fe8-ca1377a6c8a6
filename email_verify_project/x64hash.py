def to_uint32(x):
    return x & 0xFFFFFFFF

def x64_add(a, b):
    low = to_uint32(a[1] + b[1])
    carry = (a[1] + b[1]) >> 32
    high = to_uint32(a[0] + b[0] + carry)
    return [high, low]

def x64_xor(a, b):
    return [a[0] ^ b[0], a[1] ^ b[1]]

def x64_left_shift(a, n):
    combined = (a[0] << 32) | a[1]
    combined = (combined << n) & ((1 << 64) - 1)
    return [combined >> 32, combined & 0xFFFFFFFF]

def x64_rotl(a, n):
    combined = ((a[0] << 32) | a[1]) & 0xFFFFFFFFFFFFFFFF
    n = n % 64
    combined = ((combined << n) | (combined >> (64 - n))) & 0xFFFFFFFFFFFFFFFF
    return [combined >> 32, combined & 0xFFFFFFFF]

def x64_multiply(a, b):
    # 模拟低位64位乘法
    combined_a = (a[0] << 32) | a[1]
    combined_b = (b[0] << 32) | b[1]
    product = (combined_a * combined_b) & 0xFFFFFFFFFFFFFFFF
    return [product >> 32, product & 0xFFFFFFFF]

def x64_fmix(h):
    h = x64_xor(h, [0, h[0] >> 1])
    h = x64_multiply(h, [0xff51afd7, 0xed558ccd])
    h = x64_xor(h, [0, h[0] >> 1])
    h = x64_multiply(h, [0xc4ceb9fe, 0x1a85ec53])
    h = x64_xor(h, [0, h[0] >> 1])
    return h

def x64hash128(key, seed):
    key = key or ""
    seed = seed or 0
    n = len(key) % 16
    o = len(key) - n
    r = [0, seed]
    i = [0, seed]
    d = [2277735313, 289559509]
    s = [1291169091, 658871167]
    u = 0

    while u < o:
        a = [
            (ord(key[u + 4]) & 0xFF) | ((ord(key[u + 5]) & 0xFF) << 8) | ((ord(key[u + 6]) & 0xFF) << 16) | ((ord(key[u + 7]) & 0xFF) << 24),
            (ord(key[u]) & 0xFF) | ((ord(key[u + 1]) & 0xFF) << 8) | ((ord(key[u + 2]) & 0xFF) << 16) | ((ord(key[u + 3]) & 0xFF) << 24)
        ]
        l = [
            (ord(key[u + 12]) & 0xFF) | ((ord(key[u + 13]) & 0xFF) << 8) | ((ord(key[u + 14]) & 0xFF) << 16) | ((ord(key[u + 15]) & 0xFF) << 24),
            (ord(key[u + 8]) & 0xFF) | ((ord(key[u + 9]) & 0xFF) << 8) | ((ord(key[u + 10]) & 0xFF) << 16) | ((ord(key[u + 11]) & 0xFF) << 24)
        ]

        a = x64_multiply(a, d)
        a = x64_rotl(a, 31)
        a = x64_multiply(a, s)
        r = x64_xor(r, a)
        r = x64_rotl(r, 27)
        r = x64_add(r, i)
        r = x64_add(x64_multiply(r, [0, 5]), [0, 1390208809])

        l = x64_multiply(l, s)
        l = x64_rotl(l, 33)
        l = x64_multiply(l, d)
        i = x64_xor(i, l)
        i = x64_rotl(i, 31)
        i = x64_add(i, r)
        i = x64_add(x64_multiply(i, [0, 5]), [0, 944331445])

        u += 16

    a = [0, 0]
    l = [0, 0]

    if n:
        if n >= 9:
            for p in range(8, n):
                l = x64_xor(l, x64_left_shift([0, ord(key[o + p])], (p - 8) * 8))
            l = x64_multiply(l, s)
            l = x64_rotl(l, 33)
            l = x64_multiply(l, d)
            i = x64_xor(i, l)

        if n >= 1:
            for p in range(min(8, n)):
                a = x64_xor(a, x64_left_shift([0, ord(key[o + p])], p * 8))
            a = x64_multiply(a, d)
            a = x64_rotl(a, 31)
            a = x64_multiply(a, s)
            r = x64_xor(r, a)

    r = x64_xor(r, [0, len(key)])
    i = x64_xor(i, [0, len(key)])
    r = x64_add(r, i)
    i = x64_add(i, r)
    r = x64_fmix(r)
    i = x64_fmix(i)
    r = x64_add(r, i)
    i = x64_add(i, r)

    return (
        ("00000000" + hex(r[0])[2:])[-8:] +
        ("00000000" + hex(r[1])[2:])[-8:] +
        ("00000000" + hex(i[0])[2:])[-8:] +
        ("00000000" + hex(i[1])[2:])[-8:]
    )


# cres = "50dLwMoACHMzn8rq"
# seed = 2274334153
# mask = "2030"
# cres = "2dFElqTgJpJ3BVaH"
# prefix = "2d"
# seed = 630609039
# mask = "daaf"

# hash_val = x64hash128(cres, seed)
# print("Hash:", hash_val)
# print("是否匹配mask？", hash_val.startswith(mask))
