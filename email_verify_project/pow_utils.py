import hashlib
import time
import random
import string

# 定义字符集
charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"

def fast_random_string(length):
    """使用random.choices高效生成随机字符串"""
    return ''.join(random.choices(charset, k=length))

def generate_random_string(length):
    """生成指定长度的随机字符串，使用高性能实现"""
    chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    # 使用字节操作直接生成随机索引，避免random.choice的开销
    result = ""
    for _ in range(length):
        index = int(random.random() * 62)  # 0-61之间的随机数
        result += chars[index]
    return result

def is_valid_chars(text):
    """检查字符串是否只包含允许的字符"""
    valid_chars = set(charset)
    return all(char in valid_chars for char in text)

def starts_with(string, prefix):
    """模拟 JavaScript 的 localeCompare 为 0 的情况"""
    return string.startswith(prefix)

# x64hash128 相关函数
def to_uint32(x):
    return x & 0xFFFFFFFF

def x64_add(a, b):
    low = to_uint32(a[1] + b[1])
    carry = (a[1] + b[1]) >> 32
    high = to_uint32(a[0] + b[0] + carry)
    return [high, low]

def x64_xor(a, b):
    return [a[0] ^ b[0], a[1] ^ b[1]]

def x64_left_shift(a, n):
    combined = (a[0] << 32) | a[1]
    combined = (combined << n) & ((1 << 64) - 1)
    return [combined >> 32, combined & 0xFFFFFFFF]

def x64_rotl(a, n):
    combined = ((a[0] << 32) | a[1]) & 0xFFFFFFFFFFFFFFFF
    n = n % 64
    combined = ((combined << n) | (combined >> (64 - n))) & 0xFFFFFFFFFFFFFFFF
    return [combined >> 32, combined & 0xFFFFFFFF]

def x64_multiply(a, b):
    # 模拟低位64位乘法
    combined_a = (a[0] << 32) | a[1]
    combined_b = (b[0] << 32) | b[1]
    product = (combined_a * combined_b) & 0xFFFFFFFFFFFFFFFF
    return [product >> 32, product & 0xFFFFFFFF]

def x64_fmix(h):
    h = x64_xor(h, [0, h[0] >> 1])
    h = x64_multiply(h, [0xff51afd7, 0xed558ccd])
    h = x64_xor(h, [0, h[0] >> 1])
    h = x64_multiply(h, [0xc4ceb9fe, 0x1a85ec53])
    h = x64_xor(h, [0, h[0] >> 1])
    return h

def x64hash128(key, seed):
    key = key or ""
    seed = seed or 0
    n = len(key) % 16
    o = len(key) - n
    r = [0, seed]
    i = [0, seed]
    d = [2277735313, 289559509]
    s = [1291169091, 658871167]
    u = 0

    while u < o:
        a = [
            (ord(key[u + 4]) & 0xFF) | ((ord(key[u + 5]) & 0xFF) << 8) | ((ord(key[u + 6]) & 0xFF) << 16) | ((ord(key[u + 7]) & 0xFF) << 24),
            (ord(key[u]) & 0xFF) | ((ord(key[u + 1]) & 0xFF) << 8) | ((ord(key[u + 2]) & 0xFF) << 16) | ((ord(key[u + 3]) & 0xFF) << 24)
        ]
        l = [
            (ord(key[u + 12]) & 0xFF) | ((ord(key[u + 13]) & 0xFF) << 8) | ((ord(key[u + 14]) & 0xFF) << 16) | ((ord(key[u + 15]) & 0xFF) << 24),
            (ord(key[u + 8]) & 0xFF) | ((ord(key[u + 9]) & 0xFF) << 8) | ((ord(key[u + 10]) & 0xFF) << 16) | ((ord(key[u + 11]) & 0xFF) << 24)
        ]

        a = x64_multiply(a, d)
        a = x64_rotl(a, 31)
        a = x64_multiply(a, s)
        r = x64_xor(r, a)
        r = x64_rotl(r, 27)
        r = x64_add(r, i)
        r = x64_add(x64_multiply(r, [0, 5]), [0, 1390208809])

        l = x64_multiply(l, s)
        l = x64_rotl(l, 33)
        l = x64_multiply(l, d)
        i = x64_xor(i, l)
        i = x64_rotl(i, 31)
        i = x64_add(i, r)
        i = x64_add(x64_multiply(i, [0, 5]), [0, 944331445])

        u += 16

    a = [0, 0]
    l = [0, 0]

    if n:
        if n >= 9:
            for p in range(8, n):
                l = x64_xor(l, x64_left_shift([0, ord(key[o + p])], (p - 8) * 8))
            l = x64_multiply(l, s)
            l = x64_rotl(l, 33)
            l = x64_multiply(l, d)
            i = x64_xor(i, l)

        if n >= 1:
            for p in range(min(8, n)):
                a = x64_xor(a, x64_left_shift([0, ord(key[o + p])], p * 8))
            a = x64_multiply(a, d)
            a = x64_rotl(a, 31)
            a = x64_multiply(a, s)
            r = x64_xor(r, a)

    r = x64_xor(r, [0, len(key)])
    i = x64_xor(i, [0, len(key)])
    r = x64_add(r, i)
    i = x64_add(i, r)
    r = x64_fmix(r)
    i = x64_fmix(i)
    r = x64_add(r, i)
    i = x64_add(i, r)

    return (
        ("00000000" + hex(r[0])[2:])[-8:] +
        ("00000000" + hex(r[1])[2:])[-8:] +
        ("00000000" + hex(i[0])[2:])[-8:] +
        ("00000000" + hex(i[1])[2:])[-8:]
    )

def solve_pow(seed, mask, prefix="", min_length=16, max_iterations=None):
    """
    解决PoW问题的高性能实现
    seed: 种子值
    mask: 目标哈希前缀
    prefix: 结果字符串的前缀(可选)
    min_length: 结果字符串的最小长度(默认16)
    max_iterations: 最大尝试次数，None表示无限制
    """
    start_time = time.time()
    iterations = 0
    while True:
        iterations += 1
        suffix = fast_random_string(min_length - len(prefix))
        candidate = prefix + suffix
        hash_val = x64hash128(candidate, seed)
        if hash_val.startswith(mask):
            duration = time.time() - start_time
            return {
                'result': candidate,
                'iterations': iterations,
                'time_sec': duration,
                'hash': hash_val,
                'key': prefix,
                'seed': seed,
                'mask': mask
            }
        # 检查是否达到最大迭代次数
        if max_iterations and iterations >= max_iterations:
            duration = time.time() - start_time
            return {
                'result': None,
                'iterations': iterations,
                'time_sec': duration,
                'hash': None,
                'key': prefix,
                'seed': seed,
                'mask': mask,
                'message': '已达到最大迭代次数限制'
            }
        if iterations % 10000 == 0:
            print(f"已尝试 {iterations} 次，用时 {time.time() - start_time:.2f}秒")
            time.sleep(0)  # 稍微yield一下，像JS setTimeout(0)

def verify_pow_result(result, prefix, seed, mask):
    """验证PoW结果是否有效"""
    # 检查结果是否为空
    if not result:
        return False, "结果为空"
        
    # 检查长度是否为16
    if len(result) != 16:
        return False, f"结果长度不正确: 期望16，实际{len(result)}"
        
    # 检查是否以正确的前缀开始
    if not result.startswith(prefix):
        return False, f"结果不以正确的前缀开始: 期望{prefix}"
        
    # 检查字符是否有效
    if not is_valid_chars(result):
        return False, "结果包含无效字符"
        
    # 计算哈希并验证
    hash_value = x64hash128(result, seed)
    if not hash_value.startswith(mask):
        return False, f"哈希值不满足mask要求: {hash_value} 应该以 {mask} 开始"
        
    return True, "验证通过"

def test_pow_solution(prefix, seed, mask, min_length=16, max_iterations=None):
    """测试PoW解决方案"""
    print(f"\n开始PoW测试:")
    print(f"前缀: {prefix}")
    print(f"种子: {seed}")
    print(f"掩码: {mask}")
    print(f"最大迭代次数: {max_iterations if max_iterations else '无限制'}")
    
    result = solve_pow(seed, mask, prefix, min_length, max_iterations)
    
    if not result.get('result'):
        print(f"\n结果详情:")
        print(f"结果: {result.get('message', '解决PoW失败')}")
        print(f"迭代次数: {result.get('iterations')}")
        print(f"耗时: {result.get('time_sec'):.2f} 秒")
        return
    
    is_valid, message = verify_pow_result(
        result.get('result'),
        prefix,
        seed,
        mask
    )
    
    print(f"\n结果详情:")
    print(f"结果字符串: {result.get('result')}")
    print(f"迭代次数: {result.get('iterations')}")
    print(f"耗时: {result.get('time_sec'):.2f} 秒")
    print(f"哈希值: {result.get('hash')}")
    print(f"验证结果: {'通过' if is_valid else '失败'}")
    print(f"验证消息: {message}")

# 测试特定的PoW值
def test_specific_pow(candidate, prefix, seed, mask):
    """测试特定的PoW值"""
    result = verify_pow_result(candidate, prefix, seed, mask)
    print("\n测试特定PoW值:")
    
    if result[0]:
        print("验证通过!")
    else:
        print(f"验证失败: {result[1]}")
        # 打印实际的哈希值以供调试
        hash_value = x64hash128(candidate, seed)
        print(f"计算得到的哈希值: {hash_value}")

def test_hash_implementation():
    """测试哈希实现的正确性"""
    test_cases = [
        {
            'input': 'eftlfb6i2eKIiNRx',
            'seed': 3388280637,
            'expected_prefix': 'fb35'
        },
        # 可以添加更多测试用例
    ]
    
    print("\n开始哈希实现测试:")
    for i, test_case in enumerate(test_cases, 1):
        input_str = test_case['input']
        seed = test_case['seed']
        expected = test_case['expected_prefix']
        
        hash_value = x64hash128(input_str, seed)
        actual_prefix = hash_value[:4]
        
        print(f"\n测试用例 {i}:")
        print(f"输入: {input_str}")
        print(f"种子: {seed}")
        print(f"期望前缀: {expected}")
        print(f"实际哈希: {hash_value}")
        print(f"实际前缀: {actual_prefix}")
        print(f"结果: {'通过' if actual_prefix == expected else '失败'}") 